package com.dentalclinic.management.repository;

import com.dentalclinic.management.entity.Patient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PatientRepository extends JpaRepository<Patient, Long> {
    
    List<Patient> findByIsActiveTrue();
    
    Page<Patient> findByIsActiveTrue(Pageable pageable);
    
    Optional<Patient> findByIdAndIsActiveTrue(Long id);
    
    Optional<Patient> findByNationalId(String nationalId);
    
    Optional<Patient> findByEmail(String email);
    
    @Query("SELECT p FROM Patient p WHERE p.isActive = true AND " +
           "(LOWER(p.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "p.phoneNumber LIKE CONCAT('%', :searchTerm, '%') OR " +
           "p.nationalId LIKE CONCAT('%', :searchTerm, '%') OR " +
           "LOWER(p.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<Patient> searchPatients(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    @Query("SELECT p FROM Patient p WHERE p.isActive = true AND " +
           "LOWER(CONCAT(p.firstName, ' ', p.lastName)) LIKE LOWER(CONCAT('%', :fullName, '%'))")
    List<Patient> findByFullNameContainingIgnoreCase(@Param("fullName") String fullName);
    
    @Query("SELECT COUNT(p) FROM Patient p WHERE p.isActive = true")
    Long countActivePatients();
    
    @Query("SELECT COUNT(p) FROM Patient p WHERE p.isActive = true AND " +
           "DATE(p.createdAt) = CURRENT_DATE")
    Long countPatientsRegisteredToday();
    
    Boolean existsByNationalId(String nationalId);
    
    Boolean existsByEmail(String email);
}
