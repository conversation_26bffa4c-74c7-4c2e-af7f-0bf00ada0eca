package com.dentalclinic.management.repository;

import com.dentalclinic.management.entity.Doctor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DoctorRepository extends JpaRepository<Doctor, Long> {
    
    List<Doctor> findByIsAvailableTrue();
    
    Optional<Doctor> findByIdAndIsAvailableTrue(Long id);
    
    Optional<Doctor> findByUserId(Long userId);
    
    @Query("SELECT d FROM Doctor d JOIN FETCH d.user WHERE d.isAvailable = true")
    List<Doctor> findAllAvailableDoctorsWithUser();
    
    @Query("SELECT d FROM Doctor d JOIN FETCH d.user WHERE d.id = :id AND d.isAvailable = true")
    Optional<Doctor> findByIdWithUser(@Param("id") Long id);
    
    @Query("SELECT d FROM Doctor d WHERE d.isAvailable = true AND " +
           "LOWER(d.specialization) LIKE LOWER(CONCAT('%', :specialization, '%'))")
    List<Doctor> findBySpecializationContainingIgnoreCase(@Param("specialization") String specialization);
    
    @Query("SELECT COUNT(d) FROM Doctor d WHERE d.isAvailable = true")
    Long countAvailableDoctors();
}
