package com.dentalclinic.management.service;

import com.dentalclinic.management.dto.DoctorDto;
import com.dentalclinic.management.entity.Doctor;
import com.dentalclinic.management.repository.DoctorRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class DoctorService {
    
    private final DoctorRepository doctorRepository;
    
    @Transactional(readOnly = true)
    public List<DoctorDto> getAllAvailableDoctors() {
        log.debug("Fetching all available doctors");
        return doctorRepository.findAllAvailableDoctorsWithUser()
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public Optional<DoctorDto> getDoctorById(Long id) {
        log.debug("Fetching doctor with ID: {}", id);
        return doctorRepository.findByIdWithUser(id)
                .map(this::mapToDto);
    }
    
    @Transactional(readOnly = true)
    public Optional<DoctorDto> getDoctorByUserId(Long userId) {
        log.debug("Fetching doctor with user ID: {}", userId);
        return doctorRepository.findByUserId(userId)
                .map(this::mapToDto);
    }
    
    @Transactional(readOnly = true)
    public List<DoctorDto> getDoctorsBySpecialization(String specialization) {
        log.debug("Fetching doctors with specialization: {}", specialization);
        return doctorRepository.findBySpecializationContainingIgnoreCase(specialization)
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public Long getTotalDoctorsCount() {
        return doctorRepository.countAvailableDoctors();
    }
    
    private DoctorDto mapToDto(Doctor doctor) {
        DoctorDto dto = new DoctorDto();
        dto.setId(doctor.getId());
        
        if (doctor.getUser() != null) {
            dto.setUserId(doctor.getUser().getId());
            dto.setFirstName(doctor.getUser().getFirstName());
            dto.setLastName(doctor.getUser().getLastName());
            dto.setFullName(doctor.getUser().getFullName());
            dto.setEmail(doctor.getUser().getEmail());
            dto.setPhoneNumber(doctor.getUser().getPhoneNumber());
        }
        
        dto.setSpecialization(doctor.getSpecialization());
        dto.setLicenseNumber(doctor.getLicenseNumber());
        dto.setYearsOfExperience(doctor.getYearsOfExperience());
        dto.setConsultationFee(doctor.getConsultationFee());
        dto.setQualifications(doctor.getQualifications());
        dto.setBiography(doctor.getBiography());
        dto.setWorkStartTime(doctor.getWorkStartTime());
        dto.setWorkEndTime(doctor.getWorkEndTime());
        dto.setIsAvailable(doctor.getIsAvailable());
        dto.setCreatedAt(doctor.getCreatedAt());
        dto.setUpdatedAt(doctor.getUpdatedAt());
        
        return dto;
    }
}
