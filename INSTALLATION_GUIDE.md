# دليل تثبيت وتشغيل نظام إدارة عيادة الأسنان

## المتطلبات الأساسية

### 1. متطلبات النظام
- **Java 17** أو أحدث
- **Maven 3.6** أو أحدث
- **MySQL 8.0** أو أحدث (اختياري - يمكن استخدام H2 للاختبار)
- **4 GB RAM** كحد أدنى
- **2 GB** مساحة فارغة على القرص الصلب

### 2. تثبيت Java
1. قم بتحميل Java 17 من [Oracle](https://www.oracle.com/java/technologies/downloads/) أو [OpenJDK](https://openjdk.org/)
2. قم بتثبيت Java واتبع التعليمات
3. تأكد من إضافة Java إلى متغير البيئة PATH
4. تحقق من التثبيت بتشغيل: `java -version`

### 3. تثبيت Maven
1. قم بتحميل Maven من [الموقع الرسمي](https://maven.apache.org/download.cgi)
2. قم بفك الضغط وإضافة مجلد bin إلى PATH
3. تحقق من التثبيت بتشغيل: `mvn -version`

### 4. تثبيت MySQL (اختياري)
1. قم بتحميل MySQL من [الموقع الرسمي](https://dev.mysql.com/downloads/)
2. قم بالتثبيت وإنشاء قاعدة بيانات جديدة:
```sql
CREATE DATABASE dental_clinic_db;
CREATE USER 'dental_user'@'localhost' IDENTIFIED BY 'dental_password';
GRANT ALL PRIVILEGES ON dental_clinic_db.* TO 'dental_user'@'localhost';
FLUSH PRIVILEGES;
```

## خطوات التشغيل

### الطريقة الأولى: استخدام ملفات التشغيل المجهزة

#### في Windows:
```cmd
run.bat
```

#### في Linux/Mac:
```bash
./run.sh
```

### الطريقة الثانية: التشغيل اليدوي

1. **استنساخ أو تحميل المشروع**
```bash
git clone <repository-url>
cd dental-clinic-management
```

2. **تحديث إعدادات قاعدة البيانات** (إذا كنت تستخدم MySQL)
قم بتحرير ملف `src/main/resources/application.yml`:
```yaml
spring:
  datasource:
    url: ********************************************
    username: dental_user
    password: dental_password
```

3. **تجميع المشروع**
```bash
mvn clean compile
```

4. **تشغيل الاختبارات** (اختياري)
```bash
mvn test
```

5. **تشغيل التطبيق**
```bash
mvn spring-boot:run
```

## الوصول للتطبيق

بعد تشغيل التطبيق بنجاح، يمكنك الوصول إلى:

### 1. التطبيق الرئيسي
- **الرابط**: http://localhost:8080/api
- **صفحة تسجيل الدخول**: http://localhost:8080/login

### 2. واجهة Swagger للـ APIs
- **الرابط**: http://localhost:8080/api/swagger-ui.html
- **JSON Documentation**: http://localhost:8080/api/api-docs

### 3. قاعدة بيانات H2 Console (إذا كنت تستخدم H2)
- **الرابط**: http://localhost:8080/api/h2-console
- **JDBC URL**: jdbc:h2:mem:testdb
- **Username**: sa
- **Password**: (فارغ)

## بيانات تسجيل الدخول الافتراضية

يتم إنشاء المستخدمين التاليين تلقائياً عند أول تشغيل:

### 1. مدير النظام
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **الصلاحيات**: جميع الصلاحيات

### 2. طبيب
- **اسم المستخدم**: doctor
- **كلمة المرور**: doctor123
- **الصلاحيات**: إدارة المرضى والمواعيد والسجلات الطبية

### 3. موظف الاستقبال
- **اسم المستخدم**: receptionist
- **كلمة المرور**: receptionist123
- **الصلاحيات**: إدارة المرضى والمواعيد والفواتير

## استكشاف الأخطاء

### 1. خطأ في الاتصال بقاعدة البيانات
- تأكد من تشغيل MySQL
- تحقق من صحة بيانات الاتصال في application.yml
- تأكد من وجود قاعدة البيانات والمستخدم

### 2. خطأ في المنفذ (Port)
- تأكد من أن المنفذ 8080 غير مستخدم
- يمكنك تغيير المنفذ في application.yml:
```yaml
server:
  port: 8081
```

### 3. خطأ في الذاكرة
- قم بزيادة ذاكرة JVM:
```bash
export MAVEN_OPTS="-Xmx2g"
mvn spring-boot:run
```

### 4. مشاكل في تجميع المشروع
- تأكد من اتصال الإنترنت لتحميل التبعيات
- امسح cache Maven:
```bash
mvn clean
mvn dependency:purge-local-repository
```

## الميزات المتاحة

### 1. إدارة المرضى
- إضافة وتعديل وحذف المرضى
- البحث المتقدم
- رفع الملفات الطبية

### 2. إدارة المواعيد
- حجز وإدارة المواعيد
- التحقق من التوفر
- إشعارات المواعيد

### 3. السجلات الطبية
- إنشاء وإدارة السجلات
- ربط السجلات بالمواعيد
- رفع التقارير والصور

### 4. إدارة الفواتير
- إنشاء فواتير تلقائية
- إدارة المدفوعات
- تقارير مالية

### 5. لوحة التحكم
- إحصائيات شاملة
- تقارير يومية وشهرية
- رسوم بيانية تفاعلية

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من ملف logs/dental-clinic.log للأخطاء
2. راجع وثائق Swagger للـ APIs
3. تأكد من تحديث جميع التبعيات

## تحديث النظام

لتحديث النظام:
1. قم بإيقاف التطبيق (Ctrl+C)
2. احصل على آخر إصدار من الكود
3. قم بتشغيل: `mvn clean install`
4. أعد تشغيل التطبيق

## النسخ الاحتياطي

لعمل نسخة احتياطية من البيانات:
1. قم بتصدير قاعدة البيانات
2. انسخ مجلد uploads/ (الملفات المرفوعة)
3. احتفظ بنسخة من ملف application.yml
