package com.dentalclinic.management.dto;

import com.dentalclinic.management.entity.InvoiceStatus;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class InvoiceDto {
    
    private Long id;
    private String invoiceNumber;
    private Long patientId;
    private String patientName;
    private String patientPhone;
    private Long appointmentId;
    private LocalDateTime appointmentDateTime;
    private LocalDateTime invoiceDate;
    private LocalDateTime dueDate;
    private BigDecimal subtotal;
    private BigDecimal taxAmount;
    private BigDecimal discountAmount;
    private BigDecimal totalAmount;
    private BigDecimal paidAmount;
    private BigDecimal remainingAmount;
    private InvoiceStatus status;
    private String notes;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private List<InvoiceItemDto> invoiceItems;
    private List<PaymentDto> payments;
    private Boolean isFullyPaid;
}
