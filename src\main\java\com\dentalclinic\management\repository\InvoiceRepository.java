package com.dentalclinic.management.repository;

import com.dentalclinic.management.entity.Invoice;
import com.dentalclinic.management.entity.InvoiceStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface InvoiceRepository extends JpaRepository<Invoice, Long> {
    
    Optional<Invoice> findByInvoiceNumber(String invoiceNumber);
    
    List<Invoice> findByPatientId(Long patientId);
    
    Page<Invoice> findByPatientId(Long patientId, Pageable pageable);
    
    List<Invoice> findByStatus(InvoiceStatus status);
    
    Page<Invoice> findByStatus(InvoiceStatus status, Pageable pageable);
    
    Optional<Invoice> findByAppointmentId(Long appointmentId);
    
    @Query("SELECT i FROM Invoice i WHERE i.invoiceDate BETWEEN :startDate AND :endDate")
    List<Invoice> findByInvoiceDateBetween(@Param("startDate") LocalDateTime startDate,
                                          @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT i FROM Invoice i WHERE i.dueDate < CURRENT_TIMESTAMP AND i.status = 'PENDING'")
    List<Invoice> findOverdueInvoices();
    
    @Query("SELECT i FROM Invoice i WHERE DATE(i.invoiceDate) = CURRENT_DATE")
    List<Invoice> findTodayInvoices();
    
    @Query("SELECT COUNT(i) FROM Invoice i WHERE DATE(i.invoiceDate) = CURRENT_DATE")
    Long countTodayInvoices();
    
    @Query("SELECT COUNT(i) FROM Invoice i WHERE i.status = :status")
    Long countByStatus(@Param("status") InvoiceStatus status);
    
    @Query("SELECT SUM(i.totalAmount) FROM Invoice i WHERE DATE(i.invoiceDate) = CURRENT_DATE")
    BigDecimal getTodayTotalRevenue();
    
    @Query("SELECT SUM(i.paidAmount) FROM Invoice i WHERE DATE(i.invoiceDate) = CURRENT_DATE")
    BigDecimal getTodayPaidAmount();
    
    @Query("SELECT SUM(i.totalAmount) FROM Invoice i WHERE i.invoiceDate BETWEEN :startDate AND :endDate")
    BigDecimal getTotalRevenueByDateRange(@Param("startDate") LocalDateTime startDate,
                                         @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT SUM(i.paidAmount) FROM Invoice i WHERE i.invoiceDate BETWEEN :startDate AND :endDate")
    BigDecimal getPaidAmountByDateRange(@Param("startDate") LocalDateTime startDate,
                                       @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT i FROM Invoice i JOIN FETCH i.patient JOIN FETCH i.invoiceItems WHERE i.id = :id")
    Optional<Invoice> findByIdWithDetails(@Param("id") Long id);
    
    Boolean existsByInvoiceNumber(String invoiceNumber);
}
