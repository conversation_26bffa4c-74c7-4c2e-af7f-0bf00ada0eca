package com.dentalclinic.management.repository;

import com.dentalclinic.management.entity.FileCategory;
import com.dentalclinic.management.entity.MedicalFile;
import com.dentalclinic.management.entity.Patient;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MedicalFileRepository extends JpaRepository<MedicalFile, Long> {
    
    List<MedicalFile> findByPatientId(Long patientId);
    
    List<MedicalFile> findByPatientIdAndFileCategory(Long patientId, FileCategory fileCategory);
    
    List<MedicalFile> findByMedicalRecordId(Long medicalRecordId);
    
    List<MedicalFile> findByPatient(Patient patient);
}
