package com.dentalclinic.management.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class MedicalReportDto {
    
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    
    // Patient Statistics
    private Long totalPatients;
    private Long newPatients;
    private Long activePatients;
    
    // Appointment Statistics
    private Long totalAppointments;
    private Long completedAppointments;
    private Long cancelledAppointments;
    private Long noShowAppointments;
    
    // Medical Record Statistics
    private Long totalMedicalRecords;
    private List<DoctorStatsDto> doctorStats;
    
    // Treatment Statistics
    private List<TreatmentStatsDto> treatmentStats;
}
