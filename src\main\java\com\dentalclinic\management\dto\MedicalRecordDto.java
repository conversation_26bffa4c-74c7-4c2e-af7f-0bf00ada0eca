package com.dentalclinic.management.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class MedicalRecordDto {
    
    private Long id;
    private Long patientId;
    private String patientName;
    private Long doctorId;
    private String doctorName;
    private Long appointmentId;
    private LocalDateTime appointmentDateTime;
    private String symptoms;
    private String diagnosis;
    private String treatment;
    private String prescription;
    private String followUpInstructions;
    private String notes;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private List<MedicalFileDto> medicalFiles;
}
