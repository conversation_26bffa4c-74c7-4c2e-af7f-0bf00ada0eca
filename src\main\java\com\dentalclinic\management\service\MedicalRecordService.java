package com.dentalclinic.management.service;

import com.dentalclinic.management.dto.MedicalFileDto;
import com.dentalclinic.management.dto.MedicalRecordCreateRequest;
import com.dentalclinic.management.dto.MedicalRecordDto;
import com.dentalclinic.management.entity.*;
import com.dentalclinic.management.repository.AppointmentRepository;
import com.dentalclinic.management.repository.DoctorRepository;
import com.dentalclinic.management.repository.MedicalRecordRepository;
import com.dentalclinic.management.repository.PatientRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class MedicalRecordService {
    
    private final MedicalRecordRepository medicalRecordRepository;
    private final PatientRepository patientRepository;
    private final DoctorRepository doctorRepository;
    private final AppointmentRepository appointmentRepository;
    
    public MedicalRecordDto createMedicalRecord(MedicalRecordCreateRequest request) {
        log.info("Creating medical record for patient ID: {} by doctor ID: {}", 
                request.getPatientId(), request.getDoctorId());
        
        // Validate patient exists
        Patient patient = patientRepository.findByIdAndIsActiveTrue(request.getPatientId())
                .orElseThrow(() -> new RuntimeException("Patient not found with ID: " + request.getPatientId()));
        
        // Validate doctor exists
        Doctor doctor = doctorRepository.findByIdAndIsAvailableTrue(request.getDoctorId())
                .orElseThrow(() -> new RuntimeException("Doctor not found with ID: " + request.getDoctorId()));
        
        // Validate appointment if provided
        Appointment appointment = null;
        if (request.getAppointmentId() != null) {
            appointment = appointmentRepository.findById(request.getAppointmentId())
                    .orElseThrow(() -> new RuntimeException("Appointment not found with ID: " + request.getAppointmentId()));
            
            // Check if medical record already exists for this appointment
            if (medicalRecordRepository.findByAppointmentId(request.getAppointmentId()).isPresent()) {
                throw new RuntimeException("Medical record already exists for this appointment");
            }
        }
        
        // Create medical record
        MedicalRecord medicalRecord = mapToEntity(request, patient, doctor, appointment);
        MedicalRecord savedRecord = medicalRecordRepository.save(medicalRecord);
        
        // Update appointment status if provided
        if (appointment != null) {
            appointment.setStatus(AppointmentStatus.COMPLETED);
            appointmentRepository.save(appointment);
        }
        
        log.info("Medical record created successfully with ID: {}", savedRecord.getId());
        return mapToDto(savedRecord);
    }
    
    @Transactional(readOnly = true)
    public Optional<MedicalRecordDto> getMedicalRecordById(Long id) {
        log.debug("Fetching medical record with ID: {}", id);
        return medicalRecordRepository.findByIdWithDetails(id)
                .map(this::mapToDto);
    }
    
    @Transactional(readOnly = true)
    public List<MedicalRecordDto> getMedicalRecordsByPatient(Long patientId) {
        log.debug("Fetching medical records for patient ID: {}", patientId);
        return medicalRecordRepository.findByPatientIdOrderByCreatedAtDesc(patientId)
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public Page<MedicalRecordDto> getMedicalRecordsByPatient(Long patientId, Pageable pageable) {
        log.debug("Fetching medical records for patient ID: {} with pagination", patientId);
        return medicalRecordRepository.findByPatientId(patientId, pageable)
                .map(this::mapToDto);
    }
    
    @Transactional(readOnly = true)
    public List<MedicalRecordDto> getMedicalRecordsByDoctor(Long doctorId) {
        log.debug("Fetching medical records for doctor ID: {}", doctorId);
        return medicalRecordRepository.findByDoctorId(doctorId)
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public Page<MedicalRecordDto> getMedicalRecordsByDoctor(Long doctorId, Pageable pageable) {
        log.debug("Fetching medical records for doctor ID: {} with pagination", doctorId);
        return medicalRecordRepository.findByDoctorId(doctorId, pageable)
                .map(this::mapToDto);
    }

    public MedicalRecordDto updateMedicalRecord(Long id, MedicalRecordCreateRequest request) {
        log.info("Updating medical record with ID: {}", id);

        MedicalRecord medicalRecord = medicalRecordRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Medical record not found with ID: " + id));

        // Update fields
        updateMedicalRecordFromRequest(medicalRecord, request);
        MedicalRecord updatedRecord = medicalRecordRepository.save(medicalRecord);

        log.info("Medical record updated successfully with ID: {}", updatedRecord.getId());
        return mapToDto(updatedRecord);
    }

    @Transactional(readOnly = true)
    public Optional<MedicalRecordDto> getMedicalRecordByAppointment(Long appointmentId) {
        log.debug("Fetching medical record for appointment ID: {}", appointmentId);
        return medicalRecordRepository.findByAppointmentId(appointmentId)
                .map(this::mapToDto);
    }

    @Transactional(readOnly = true)
    public List<MedicalRecordDto> getMedicalRecordsByDateRange(Long patientId,
                                                              LocalDateTime startDate,
                                                              LocalDateTime endDate) {
        log.debug("Fetching medical records for patient ID: {} between {} and {}",
                patientId, startDate, endDate);
        return medicalRecordRepository.findByPatientAndDateRange(patientId, startDate, endDate)
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public Long getMedicalRecordsCountByPatient(Long patientId) {
        return medicalRecordRepository.countByPatientId(patientId);
    }

    @Transactional(readOnly = true)
    public Long getMedicalRecordsCountByDoctor(Long doctorId) {
        return medicalRecordRepository.countByDoctorId(doctorId);
    }

    @Transactional(readOnly = true)
    public Long getTodayMedicalRecordsCount() {
        return medicalRecordRepository.countTodayRecords();
    }

    private MedicalRecord mapToEntity(MedicalRecordCreateRequest request, Patient patient,
                                    Doctor doctor, Appointment appointment) {
        MedicalRecord medicalRecord = new MedicalRecord();
        medicalRecord.setPatient(patient);
        medicalRecord.setDoctor(doctor);
        medicalRecord.setAppointment(appointment);
        updateMedicalRecordFromRequest(medicalRecord, request);
        return medicalRecord;
    }

    private void updateMedicalRecordFromRequest(MedicalRecord medicalRecord,
                                              MedicalRecordCreateRequest request) {
        medicalRecord.setSymptoms(request.getSymptoms());
        medicalRecord.setDiagnosis(request.getDiagnosis());
        medicalRecord.setTreatment(request.getTreatment());
        medicalRecord.setPrescription(request.getPrescription());
        medicalRecord.setFollowUpInstructions(request.getFollowUpInstructions());
        medicalRecord.setNotes(request.getNotes());
    }

    private MedicalRecordDto mapToDto(MedicalRecord medicalRecord) {
        MedicalRecordDto dto = new MedicalRecordDto();
        dto.setId(medicalRecord.getId());
        dto.setPatientId(medicalRecord.getPatient().getId());
        dto.setPatientName(medicalRecord.getPatient().getFullName());
        dto.setDoctorId(medicalRecord.getDoctor().getId());
        dto.setDoctorName(medicalRecord.getDoctor().getFullName());

        if (medicalRecord.getAppointment() != null) {
            dto.setAppointmentId(medicalRecord.getAppointment().getId());
            dto.setAppointmentDateTime(medicalRecord.getAppointment().getAppointmentDateTime());
        }

        dto.setSymptoms(medicalRecord.getSymptoms());
        dto.setDiagnosis(medicalRecord.getDiagnosis());
        dto.setTreatment(medicalRecord.getTreatment());
        dto.setPrescription(medicalRecord.getPrescription());
        dto.setFollowUpInstructions(medicalRecord.getFollowUpInstructions());
        dto.setNotes(medicalRecord.getNotes());
        dto.setCreatedAt(medicalRecord.getCreatedAt());
        dto.setUpdatedAt(medicalRecord.getUpdatedAt());

        // Map medical files
        if (medicalRecord.getMedicalFiles() != null) {
            List<MedicalFileDto> fileDtos = medicalRecord.getMedicalFiles().stream()
                    .map(this::mapMedicalFileToDto)
                    .collect(Collectors.toList());
            dto.setMedicalFiles(fileDtos);
        }

        return dto;
    }

    private MedicalFileDto mapMedicalFileToDto(MedicalFile medicalFile) {
        MedicalFileDto dto = new MedicalFileDto();
        dto.setId(medicalFile.getId());
        dto.setMedicalRecordId(medicalFile.getMedicalRecord() != null ?
                              medicalFile.getMedicalRecord().getId() : null);
        dto.setPatientId(medicalFile.getPatient().getId());
        dto.setFileName(medicalFile.getFileName());
        dto.setFilePath(medicalFile.getFilePath());
        dto.setFileType(medicalFile.getFileType());
        dto.setFileSize(medicalFile.getFileSize());
        dto.setDescription(medicalFile.getDescription());
        dto.setFileCategory(medicalFile.getFileCategory());
        dto.setCreatedAt(medicalFile.getCreatedAt());
        return dto;
    }
}
