@echo off
chcp 65001 >nul
cls
echo ========================================
echo   🦷 نظام إدارة عيادة الأسنان
echo   Dental Clinic Management System  
echo ========================================
echo.

REM Check if Java is available
java -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Java متوفر - جاري تشغيل التطبيق...
    goto :run_app
)

echo ❌ Java غير مثبت
echo.
echo 🔄 محاولة تحميل Java المحمول...

REM Create portable java directory
if not exist "portable-java" mkdir "portable-java"
if not exist "temp" mkdir "temp"

echo 📥 تحميل Java 17 المحمول...
echo (هذا قد يستغرق بضع دقائق)

REM Download Java using PowerShell
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; try { Invoke-WebRequest -Uri 'https://github.com/adoptium/temurin17-binaries/releases/download/jdk-********+1/OpenJDK17U-jdk_x64_windows_hotspot_********_1.zip' -OutFile 'temp\java17.zip' -UseBasicParsing; Write-Host 'تم التحميل بنجاح' } catch { Write-Host 'فشل التحميل:' $_.Exception.Message }}"

if not exist "temp\java17.zip" (
    echo ❌ فشل في تحميل Java
    echo.
    echo 📋 يرجى تحميل Java يدوياً:
    echo    1. اذهب إلى: https://adoptium.net/
    echo    2. حمل Eclipse Temurin 17 LTS
    echo    3. ثبته مع تفعيل "Add to PATH"
    echo    4. أعد تشغيل هذا الملف
    echo.
    start https://adoptium.net/
    pause
    exit /b 1
)

echo 📦 استخراج Java...
powershell -Command "Expand-Archive -Path 'temp\java17.zip' -DestinationPath 'portable-java' -Force"

REM Find the extracted Java directory
for /d %%i in ("portable-java\*") do (
    if exist "%%i\bin\java.exe" (
        set "JAVA_HOME=%%i"
        set "PATH=%%i\bin;%PATH%"
        echo ✅ تم إعداد Java المحمول
        goto :cleanup_and_run
    )
)

echo ❌ فشل في إعداد Java المحمول
goto :manual_install

:cleanup_and_run
echo 🧹 تنظيف الملفات المؤقتة...
if exist "temp" rmdir /s /q "temp"

:run_app
echo.
echo 🚀 بدء تشغيل نظام إدارة عيادة الأسنان...
echo.

REM Show Java version
java -version 2>&1 | findstr "version"
echo.

echo 🌐 سيكون التطبيق متاحاً على:
echo    📱 http://localhost:8080/
echo    🔐 http://localhost:8080/login  
echo    📚 http://localhost:8080/api/swagger-ui.html
echo    🗄️ http://localhost:8080/api/h2-console
echo.
echo 🔑 بيانات تسجيل الدخول:
echo    👨‍💼 مدير: admin / admin123
echo    👨‍⚕️ طبيب: doctor / doctor123  
echo    👩‍💼 استقبال: receptionist / receptionist123
echo.
echo ⏳ جاري تشغيل التطبيق...
echo    (المرة الأولى قد تستغرق 3-5 دقائق)
echo.
echo 🛑 لإيقاف التطبيق اضغط Ctrl+C
echo.

REM Start the application
if exist "mvnw.cmd" (
    echo 🔧 استخدام Maven Wrapper...
    call mvnw.cmd spring-boot:run
) else (
    echo ❌ Maven Wrapper غير موجود
    echo 🔧 محاولة استخدام Maven العادي...
    mvn spring-boot:run
)

goto :end

:manual_install
echo.
echo 📋 تعليمات التثبيت اليدوي:
echo.
echo 1️⃣ اذهب إلى: https://adoptium.net/
echo 2️⃣ حمل Eclipse Temurin 17 LTS
echo 3️⃣ ثبته مع تفعيل "Add to PATH"
echo 4️⃣ أعد تشغيل Command Prompt
echo 5️⃣ شغل هذا الملف مرة أخرى
echo.
start https://adoptium.net/
pause

:end
echo.
echo 🏁 انتهى تشغيل التطبيق
pause
