package com.dentalclinic.management.service;

import com.dentalclinic.management.dto.AppointmentCreateRequest;
import com.dentalclinic.management.dto.AppointmentDto;
import com.dentalclinic.management.entity.Appointment;
import com.dentalclinic.management.entity.AppointmentStatus;
import com.dentalclinic.management.entity.Doctor;
import com.dentalclinic.management.entity.Patient;
import com.dentalclinic.management.repository.AppointmentRepository;
import com.dentalclinic.management.repository.DoctorRepository;
import com.dentalclinic.management.repository.PatientRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AppointmentService {
    
    private final AppointmentRepository appointmentRepository;
    private final PatientRepository patientRepository;
    private final DoctorRepository doctorRepository;
    
    public AppointmentDto createAppointment(AppointmentCreateRequest request) {
        log.info("Creating new appointment for patient ID: {} with doctor ID: {}", 
                request.getPatientId(), request.getDoctorId());
        
        // Validate patient exists
        Patient patient = patientRepository.findByIdAndIsActiveTrue(request.getPatientId())
                .orElseThrow(() -> new RuntimeException("Patient not found with ID: " + request.getPatientId()));
        
        // Validate doctor exists and is available
        Doctor doctor = doctorRepository.findByIdAndIsAvailableTrue(request.getDoctorId())
                .orElseThrow(() -> new RuntimeException("Doctor not found or not available with ID: " + request.getDoctorId()));
        
        // Validate appointment time
        validateAppointmentTime(request.getAppointmentDateTime(), doctor);
        
        // Check for conflicts
        checkForConflicts(request.getDoctorId(), request.getAppointmentDateTime(), request.getDurationMinutes());
        
        // Create appointment
        Appointment appointment = mapToEntity(request, patient, doctor);
        Appointment savedAppointment = appointmentRepository.save(appointment);
        
        log.info("Appointment created successfully with ID: {}", savedAppointment.getId());
        return mapToDto(savedAppointment);
    }
    
    @Transactional(readOnly = true)
    public Optional<AppointmentDto> getAppointmentById(Long id) {
        log.debug("Fetching appointment with ID: {}", id);
        return appointmentRepository.findByIdWithDetails(id)
                .map(this::mapToDto);
    }
    
    @Transactional(readOnly = true)
    public Page<AppointmentDto> getAllAppointments(Pageable pageable) {
        log.debug("Fetching all appointments with pagination");
        return appointmentRepository.findAll(pageable)
                .map(this::mapToDto);
    }
    
    @Transactional(readOnly = true)
    public List<AppointmentDto> getAppointmentsByPatient(Long patientId) {
        log.debug("Fetching appointments for patient ID: {}", patientId);
        return appointmentRepository.findByPatientId(patientId)
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public List<AppointmentDto> getAppointmentsByDoctor(Long doctorId) {
        log.debug("Fetching appointments for doctor ID: {}", doctorId);
        return appointmentRepository.findByDoctorId(doctorId)
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public List<AppointmentDto> getTodayAppointments() {
        log.debug("Fetching today's appointments");
        return appointmentRepository.findTodayAppointments()
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public List<AppointmentDto> getTodayAppointmentsByDoctor(Long doctorId) {
        log.debug("Fetching today's appointments for doctor ID: {}", doctorId);
        return appointmentRepository.findTodayAppointmentsByDoctor(doctorId)
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    public AppointmentDto updateAppointmentStatus(Long id, AppointmentStatus status, String reason) {
        log.info("Updating appointment ID: {} to status: {}", id, status);

        Appointment appointment = appointmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Appointment not found with ID: " + id));

        appointment.setStatus(status);
        if (status == AppointmentStatus.CANCELLED && reason != null) {
            appointment.setCancellationReason(reason);
        }

        Appointment updatedAppointment = appointmentRepository.save(appointment);
        log.info("Appointment status updated successfully");

        return mapToDto(updatedAppointment);
    }

    public AppointmentDto rescheduleAppointment(Long id, LocalDateTime newDateTime) {
        log.info("Rescheduling appointment ID: {} to new time: {}", id, newDateTime);

        Appointment appointment = appointmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Appointment not found with ID: " + id));

        // Validate new appointment time
        validateAppointmentTime(newDateTime, appointment.getDoctor());

        // Check for conflicts (excluding current appointment)
        checkForConflictsExcluding(appointment.getDoctor().getId(), newDateTime,
                                 appointment.getDurationMinutes(), id);

        appointment.setAppointmentDateTime(newDateTime);
        appointment.setStatus(AppointmentStatus.SCHEDULED);
        appointment.setCancellationReason(null);

        Appointment updatedAppointment = appointmentRepository.save(appointment);
        log.info("Appointment rescheduled successfully");

        return mapToDto(updatedAppointment);
    }

    @Transactional(readOnly = true)
    public boolean isTimeSlotAvailable(Long doctorId, LocalDateTime dateTime, Integer durationMinutes) {
        return isTimeSlotAvailable(doctorId, dateTime, durationMinutes, null);
    }

    @Transactional(readOnly = true)
    public boolean isTimeSlotAvailable(Long doctorId, LocalDateTime dateTime,
                                     Integer durationMinutes, Long excludeAppointmentId) {

        LocalDateTime endTime = dateTime.plusMinutes(durationMinutes);
        List<Appointment> conflicts = appointmentRepository.findConflictingAppointments(
                doctorId, dateTime, endTime);

        if (excludeAppointmentId != null) {
            conflicts = conflicts.stream()
                    .filter(a -> !a.getId().equals(excludeAppointmentId))
                    .collect(Collectors.toList());
        }

        return conflicts.isEmpty();
    }

    @Transactional(readOnly = true)
    public Long getTodayAppointmentsCount() {
        return appointmentRepository.countTodayAppointments();
    }

    @Transactional(readOnly = true)
    public Long getTodayAppointmentsCountByStatus(AppointmentStatus status) {
        return appointmentRepository.countTodayAppointmentsByStatus(status);
    }

    private void validateAppointmentTime(LocalDateTime appointmentDateTime, Doctor doctor) {
        // Check if appointment is in the past
        if (appointmentDateTime.isBefore(LocalDateTime.now())) {
            throw new RuntimeException("Appointment cannot be scheduled in the past");
        }

        // Check if appointment is within doctor's working hours
        LocalTime appointmentTime = appointmentDateTime.toLocalTime();
        if (doctor.getWorkStartTime() != null && doctor.getWorkEndTime() != null) {
            if (appointmentTime.isBefore(doctor.getWorkStartTime()) ||
                appointmentTime.isAfter(doctor.getWorkEndTime())) {
                throw new RuntimeException("Appointment time is outside doctor's working hours");
            }
        }
    }

    private void checkForConflicts(Long doctorId, LocalDateTime dateTime, Integer durationMinutes) {
        checkForConflictsExcluding(doctorId, dateTime, durationMinutes, null);
    }

    private void checkForConflictsExcluding(Long doctorId, LocalDateTime dateTime,
                                          Integer durationMinutes, Long excludeAppointmentId) {
        if (!isTimeSlotAvailable(doctorId, dateTime, durationMinutes, excludeAppointmentId)) {
            throw new RuntimeException("Time slot is not available. There is a conflicting appointment.");
        }
    }

    private Appointment mapToEntity(AppointmentCreateRequest request, Patient patient, Doctor doctor) {
        Appointment appointment = new Appointment();
        appointment.setPatient(patient);
        appointment.setDoctor(doctor);
        appointment.setAppointmentDateTime(request.getAppointmentDateTime());
        appointment.setDurationMinutes(request.getDurationMinutes());
        appointment.setAppointmentType(request.getAppointmentType());
        appointment.setNotes(request.getNotes());
        appointment.setStatus(AppointmentStatus.SCHEDULED);
        appointment.setReminderSent(false);
        return appointment;
    }

    private AppointmentDto mapToDto(Appointment appointment) {
        AppointmentDto dto = new AppointmentDto();
        dto.setId(appointment.getId());
        dto.setPatientId(appointment.getPatient().getId());
        dto.setPatientName(appointment.getPatient().getFullName());
        dto.setPatientPhone(appointment.getPatient().getPhoneNumber());
        dto.setDoctorId(appointment.getDoctor().getId());
        dto.setDoctorName(appointment.getDoctor().getFullName());
        dto.setDoctorSpecialization(appointment.getDoctor().getSpecialization());
        dto.setAppointmentDateTime(appointment.getAppointmentDateTime());
        dto.setDurationMinutes(appointment.getDurationMinutes());
        dto.setStatus(appointment.getStatus());
        dto.setAppointmentType(appointment.getAppointmentType());
        dto.setNotes(appointment.getNotes());
        dto.setCancellationReason(appointment.getCancellationReason());
        dto.setReminderSent(appointment.getReminderSent());
        dto.setCreatedAt(appointment.getCreatedAt());
        dto.setUpdatedAt(appointment.getUpdatedAt());
        return dto;
    }
}
