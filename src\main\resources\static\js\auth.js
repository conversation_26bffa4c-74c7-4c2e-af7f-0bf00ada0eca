// Authentication JavaScript for Dental Clinic Management System

class AuthService {
    constructor() {
        this.baseURL = '/api';
        this.token = localStorage.getItem('authToken');
        this.user = JSON.parse(localStorage.getItem('user') || 'null');
    }

    // Login function
    async login(username, password) {
        try {
            const response = await fetch(`${this.baseURL}/auth/signin`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (response.ok) {
                this.token = data.token;
                this.user = {
                    id: data.id,
                    username: data.username,
                    email: data.email,
                    firstName: data.firstName,
                    lastName: data.lastName,
                    roles: data.roles
                };

                localStorage.setItem('authToken', this.token);
                localStorage.setItem('user', JSON.stringify(this.user));

                return { success: true, user: this.user };
            } else {
                return { success: false, message: data.message || 'فشل في تسجيل الدخول' };
            }
        } catch (error) {
            console.error('Login error:', error);
            return { success: false, message: 'خطأ في الاتصال بالخادم' };
        }
    }

    // Logout function
    logout() {
        this.token = null;
        this.user = null;
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
        window.location.href = '/login.html';
    }

    // Check if user is authenticated
    isAuthenticated() {
        return this.token !== null && this.user !== null;
    }

    // Get current user
    getCurrentUser() {
        return this.user;
    }

    // Get auth token
    getToken() {
        return this.token;
    }

    // Check if user has specific role
    hasRole(role) {
        return this.user && this.user.roles && this.user.roles.includes(role);
    }

    // Make authenticated API request
    async apiRequest(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            }
        };

        const mergedOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(`${this.baseURL}${url}`, mergedOptions);
            
            if (response.status === 401) {
                this.logout();
                return null;
            }

            return response;
        } catch (error) {
            console.error('API request error:', error);
            throw error;
        }
    }
}

// Create global auth service instance
const authService = new AuthService();

// Login form handler
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            const alertContainer = document.getElementById('alertContainer');
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
            
            try {
                const result = await authService.login(username, password);
                
                if (result.success) {
                    showAlert('تم تسجيل الدخول بنجاح!', 'success', alertContainer);
                    setTimeout(() => {
                        window.location.href = '/dashboard.html';
                    }, 1000);
                } else {
                    showAlert(result.message, 'danger', alertContainer);
                }
            } catch (error) {
                showAlert('خطأ في تسجيل الدخول', 'danger', alertContainer);
            } finally {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول';
            }
        });
    }
    
    // Check authentication on protected pages
    if (window.location.pathname !== '/login.html' && !authService.isAuthenticated()) {
        window.location.href = '/login.html';
    }
    
    // Update user info in navbar
    updateUserInfo();
});

// Show alert function
function showAlert(message, type, container) {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    container.innerHTML = alertHTML;
}

// Update user info in navigation
function updateUserInfo() {
    const user = authService.getCurrentUser();
    const userNameElement = document.getElementById('userName');
    
    if (user && userNameElement) {
        userNameElement.textContent = `${user.firstName} ${user.lastName}`;
    }
}

// Global logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        authService.logout();
    }
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(date);
}

// Format datetime
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
}

// Show loading spinner
function showLoading(element) {
    element.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';
}

// Hide loading spinner
function hideLoading() {
    // Implementation depends on specific use case
}

// Handle API errors
function handleApiError(error, container) {
    console.error('API Error:', error);
    if (container) {
        showAlert('حدث خطأ أثناء تحميل البيانات', 'danger', container);
    }
}

// Debounce function for search
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
