package com.dentalclinic.management.dto;

import com.dentalclinic.management.entity.PaymentMethod;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class PaymentDto {
    
    private Long id;
    private Long invoiceId;
    private String invoiceNumber;
    private BigDecimal paymentAmount;
    private PaymentMethod paymentMethod;
    private LocalDateTime paymentDate;
    private String transactionReference;
    private String notes;
    private LocalDateTime createdAt;
}
