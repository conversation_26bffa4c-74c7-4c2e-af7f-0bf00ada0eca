package com.dentalclinic.management.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DailyRevenueDto {
    
    private LocalDate date;
    private BigDecimal revenue;
    private BigDecimal paidAmount;
    private Long invoiceCount;
    private Long paymentCount;
}
