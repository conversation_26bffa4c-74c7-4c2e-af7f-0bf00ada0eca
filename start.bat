@echo off
echo ========================================
echo   نظام إدارة عيادة الأسنان
echo   Dental Clinic Management System
echo ========================================
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java غير مثبت على النظام
    echo.
    echo 📋 يرجى تثبيت Java أولاً:
    echo.
    echo 1. اذهب إلى: https://adoptium.net/
    echo 2. حمل Eclipse Temurin 17 LTS
    echo 3. قم بتثبيته مع تفعيل خيار "Add to PATH"
    echo 4. أعد تشغيل Command Prompt
    echo 5. شغل هذا الملف مرة أخرى
    echo.
    echo أو استخدم الرابط المباشر:
    echo https://github.com/adoptium/temurin17-binaries/releases/download/jdk-********+1/OpenJDK17U-jdk_x64_windows_hotspot_********_1.msi
    echo.
    pause
    exit /b 1
)

echo ✅ Java متوفر
java -version
echo.

REM Try to run with Maven Wrapper
if exist "mvnw.cmd" (
    echo 🚀 بدء تشغيل التطبيق...
    echo.
    echo 🌐 سيكون التطبيق متاحاً على:
    echo    - الصفحة الرئيسية: http://localhost:8080/
    echo    - تسجيل الدخول: http://localhost:8080/login
    echo    - Swagger API: http://localhost:8080/api/swagger-ui.html
    echo    - قاعدة البيانات H2: http://localhost:8080/api/h2-console
    echo.
    echo 🔐 بيانات تسجيل الدخول:
    echo    - مدير: admin / admin123
    echo    - طبيب: doctor / doctor123
    echo    - استقبال: receptionist / receptionist123
    echo.
    echo 📊 بيانات قاعدة البيانات H2:
    echo    - JDBC URL: jdbc:h2:mem:dental_clinic_db
    echo    - Username: sa
    echo    - Password: (فارغ)
    echo.
    echo ⏳ جاري تحميل التبعيات وتشغيل التطبيق...
    echo    (قد يستغرق بضع دقائق في المرة الأولى)
    echo.
    echo 🛑 لإيقاف التطبيق اضغط Ctrl+C
    echo.
    
    .\mvnw.cmd spring-boot:run
) else (
    echo ❌ Maven Wrapper غير موجود
    echo يرجى تشغيل الأمر التالي لإنشاء Maven Wrapper:
    echo mvn wrapper:wrapper
    pause
)

pause
