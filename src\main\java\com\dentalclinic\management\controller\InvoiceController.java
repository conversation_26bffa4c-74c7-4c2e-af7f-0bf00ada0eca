package com.dentalclinic.management.controller;

import com.dentalclinic.management.dto.InvoiceCreateRequest;
import com.dentalclinic.management.dto.InvoiceDto;
import com.dentalclinic.management.dto.MessageResponse;
import com.dentalclinic.management.entity.InvoiceStatus;
import com.dentalclinic.management.service.InvoiceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/invoices")
@RequiredArgsConstructor
@Tag(name = "Invoice Management", description = "APIs for managing invoices")
@CrossOrigin(origins = "*", maxAge = 3600)
public class InvoiceController {
    
    private final InvoiceService invoiceService;
    
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Create a new invoice", description = "Creates a new invoice")
    public ResponseEntity<?> createInvoice(@Valid @RequestBody InvoiceCreateRequest request) {
        try {
            InvoiceDto createdInvoice = invoiceService.createInvoice(request);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdInvoice);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get invoice by ID", description = "Retrieves an invoice by its ID")
    public ResponseEntity<?> getInvoiceById(
            @Parameter(description = "Invoice ID") @PathVariable Long id) {
        return invoiceService.getInvoiceById(id)
                .map(invoice -> ResponseEntity.ok().body(invoice))
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/number/{invoiceNumber}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get invoice by number", description = "Retrieves an invoice by its number")
    public ResponseEntity<?> getInvoiceByNumber(
            @Parameter(description = "Invoice Number") @PathVariable String invoiceNumber) {
        return invoiceService.getInvoiceByNumber(invoiceNumber)
                .map(invoice -> ResponseEntity.ok().body(invoice))
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/patient/{patientId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get invoices by patient", description = "Retrieves all invoices for a specific patient")
    public ResponseEntity<List<InvoiceDto>> getInvoicesByPatient(
            @Parameter(description = "Patient ID") @PathVariable Long patientId) {
        
        List<InvoiceDto> invoices = invoiceService.getInvoicesByPatient(patientId);
        return ResponseEntity.ok(invoices);
    }
    
    @GetMapping("/patient/{patientId}/paginated")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get invoices by patient with pagination", description = "Retrieves invoices for a patient with pagination")
    public ResponseEntity<Page<InvoiceDto>> getInvoicesByPatientPaginated(
            @Parameter(description = "Patient ID") @PathVariable Long patientId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "invoiceDate") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<InvoiceDto> invoices = invoiceService.getInvoicesByPatient(patientId, pageable);
        return ResponseEntity.ok(invoices);
    }
    
    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get invoices by status", description = "Retrieves all invoices with a specific status")
    public ResponseEntity<List<InvoiceDto>> getInvoicesByStatus(
            @Parameter(description = "Invoice Status") @PathVariable InvoiceStatus status) {
        
        List<InvoiceDto> invoices = invoiceService.getInvoicesByStatus(status);
        return ResponseEntity.ok(invoices);
    }
    
    @GetMapping("/status/{status}/paginated")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get invoices by status with pagination", description = "Retrieves invoices with a specific status with pagination")
    public ResponseEntity<Page<InvoiceDto>> getInvoicesByStatusPaginated(
            @Parameter(description = "Invoice Status") @PathVariable InvoiceStatus status,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "invoiceDate") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<InvoiceDto> invoices = invoiceService.getInvoicesByStatus(status, pageable);
        return ResponseEntity.ok(invoices);
    }

    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Update invoice status", description = "Updates the status of an invoice")
    public ResponseEntity<?> updateInvoiceStatus(
            @Parameter(description = "Invoice ID") @PathVariable Long id,
            @Parameter(description = "New status") @RequestParam InvoiceStatus status) {

        try {
            InvoiceDto updatedInvoice = invoiceService.updateInvoiceStatus(id, status);
            return ResponseEntity.ok(updatedInvoice);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }

    @GetMapping("/overdue")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get overdue invoices", description = "Retrieves all overdue invoices")
    public ResponseEntity<List<InvoiceDto>> getOverdueInvoices() {
        List<InvoiceDto> invoices = invoiceService.getOverdueInvoices();
        return ResponseEntity.ok(invoices);
    }

    @GetMapping("/today")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get today's invoices", description = "Retrieves all invoices created today")
    public ResponseEntity<List<InvoiceDto>> getTodayInvoices() {
        List<InvoiceDto> invoices = invoiceService.getTodayInvoices();
        return ResponseEntity.ok(invoices);
    }

    @GetMapping("/stats/today/count")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get today's invoices count", description = "Returns the total number of invoices created today")
    public ResponseEntity<Long> getTodayInvoicesCount() {
        Long count = invoiceService.getTodayInvoicesCount();
        return ResponseEntity.ok(count);
    }

    @GetMapping("/stats/status/{status}/count")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get invoices count by status", description = "Returns the number of invoices with a specific status")
    public ResponseEntity<Long> getInvoicesCountByStatus(
            @Parameter(description = "Invoice Status") @PathVariable InvoiceStatus status) {

        Long count = invoiceService.getInvoicesCountByStatus(status);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/stats/today/revenue")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get today's total revenue", description = "Returns the total revenue for today")
    public ResponseEntity<BigDecimal> getTodayTotalRevenue() {
        BigDecimal revenue = invoiceService.getTodayTotalRevenue();
        return ResponseEntity.ok(revenue);
    }

    @GetMapping("/stats/today/paid")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get today's paid amount", description = "Returns the total paid amount for today")
    public ResponseEntity<BigDecimal> getTodayPaidAmount() {
        BigDecimal paidAmount = invoiceService.getTodayPaidAmount();
        return ResponseEntity.ok(paidAmount);
    }

    @GetMapping("/stats/revenue")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get revenue by date range", description = "Returns the total revenue for a date range")
    public ResponseEntity<BigDecimal> getTotalRevenueByDateRange(
            @Parameter(description = "Start date") @RequestParam
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date") @RequestParam
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {

        BigDecimal revenue = invoiceService.getTotalRevenueByDateRange(startDate, endDate);
        return ResponseEntity.ok(revenue);
    }

    @GetMapping("/stats/paid")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get paid amount by date range", description = "Returns the total paid amount for a date range")
    public ResponseEntity<BigDecimal> getPaidAmountByDateRange(
            @Parameter(description = "Start date") @RequestParam
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date") @RequestParam
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {

        BigDecimal paidAmount = invoiceService.getPaidAmountByDateRange(startDate, endDate);
        return ResponseEntity.ok(paidAmount);
    }
}
