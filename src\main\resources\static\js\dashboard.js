// Dashboard JavaScript for Dental Clinic Management System

class DashboardManager {
    constructor() {
        this.charts = {};
        this.init();
    }

    async init() {
        await this.loadDashboardStats();
        this.initializeCharts();
        await this.loadUpcomingAppointments();
        await this.loadRecentPatients();
    }

    // Load dashboard statistics
    async loadDashboardStats() {
        try {
            const response = await authService.apiRequest('/dashboard/stats');
            
            if (response && response.ok) {
                const stats = await response.json();
                this.updateStatsCards(stats);
            }
        } catch (error) {
            console.error('Error loading dashboard stats:', error);
        }
    }

    // Update statistics cards
    updateStatsCards(stats) {
        document.getElementById('totalPatients').textContent = stats.totalPatients || 0;
        document.getElementById('todayAppointments').textContent = stats.todayAppointments || 0;
        document.getElementById('todayRevenue').textContent = formatCurrency(stats.todayRevenue || 0);
        document.getElementById('pendingInvoices').textContent = stats.pendingInvoices || 0;
    }

    // Initialize charts
    initializeCharts() {
        this.initRevenueChart();
        this.initPaymentMethodChart();
    }

    // Initialize revenue chart
    initRevenueChart() {
        const ctx = document.getElementById('revenueChart').getContext('2d');
        
        this.charts.revenue = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الإيرادات',
                    data: [12000, 19000, 15000, 25000, 22000, 30000],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    }

    // Initialize payment method chart
    initPaymentMethodChart() {
        const ctx = document.getElementById('paymentMethodChart').getContext('2d');
        
        this.charts.paymentMethod = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['نقدي', 'بطاقة ائتمان', 'تأمين', 'تحويل بنكي'],
                datasets: [{
                    data: [45, 30, 15, 10],
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e'
                    ],
                    hoverBackgroundColor: [
                        '#2e59d9',
                        '#17a673',
                        '#2c9faf',
                        '#f4b619'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Load upcoming appointments
    async loadUpcomingAppointments() {
        try {
            const response = await authService.apiRequest('/appointments/today');
            
            if (response && response.ok) {
                const appointments = await response.json();
                this.displayUpcomingAppointments(appointments.slice(0, 5)); // Show only first 5
            }
        } catch (error) {
            console.error('Error loading upcoming appointments:', error);
            document.getElementById('upcomingAppointments').innerHTML = 
                '<p class="text-muted">خطأ في تحميل المواعيد</p>';
        }
    }

    // Display upcoming appointments
    displayUpcomingAppointments(appointments) {
        const container = document.getElementById('upcomingAppointments');
        
        if (appointments.length === 0) {
            container.innerHTML = '<p class="text-muted">لا توجد مواعيد اليوم</p>';
            return;
        }

        const appointmentsHTML = appointments.map(appointment => `
            <div class="d-flex align-items-center mb-3">
                <div class="me-3">
                    <div class="icon-circle bg-primary">
                        <i class="fas fa-calendar text-white"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="small text-gray-500">${formatDateTime(appointment.appointmentDateTime)}</div>
                    <div class="font-weight-bold">${appointment.patientName}</div>
                    <div class="small">د. ${appointment.doctorName}</div>
                </div>
                <div>
                    <span class="badge bg-${this.getStatusColor(appointment.status)}">${this.getStatusText(appointment.status)}</span>
                </div>
            </div>
        `).join('');

        container.innerHTML = appointmentsHTML;
    }

    // Load recent patients
    async loadRecentPatients() {
        try {
            const response = await authService.apiRequest('/patients?page=0&size=5&sortBy=createdAt&sortDir=desc');
            
            if (response && response.ok) {
                const data = await response.json();
                this.displayRecentPatients(data.content || []);
            }
        } catch (error) {
            console.error('Error loading recent patients:', error);
            document.getElementById('recentPatients').innerHTML = 
                '<p class="text-muted">خطأ في تحميل المرضى</p>';
        }
    }

    // Display recent patients
    displayRecentPatients(patients) {
        const container = document.getElementById('recentPatients');
        
        if (patients.length === 0) {
            container.innerHTML = '<p class="text-muted">لا توجد مرضى جدد</p>';
            return;
        }

        const patientsHTML = patients.map(patient => `
            <div class="d-flex align-items-center mb-3">
                <div class="me-3">
                    <div class="icon-circle bg-success">
                        <i class="fas fa-user text-white"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="font-weight-bold">${patient.fullName}</div>
                    <div class="small text-gray-500">${patient.phoneNumber || 'لا يوجد رقم هاتف'}</div>
                    <div class="small text-gray-500">تاريخ التسجيل: ${formatDate(patient.createdAt)}</div>
                </div>
            </div>
        `).join('');

        container.innerHTML = patientsHTML;
    }

    // Get status color for badges
    getStatusColor(status) {
        const colors = {
            'SCHEDULED': 'primary',
            'CONFIRMED': 'info',
            'IN_PROGRESS': 'warning',
            'COMPLETED': 'success',
            'CANCELLED': 'danger',
            'NO_SHOW': 'secondary'
        };
        return colors[status] || 'secondary';
    }

    // Get status text in Arabic
    getStatusText(status) {
        const texts = {
            'SCHEDULED': 'مجدول',
            'CONFIRMED': 'مؤكد',
            'IN_PROGRESS': 'جاري',
            'COMPLETED': 'مكتمل',
            'CANCELLED': 'ملغي',
            'NO_SHOW': 'لم يحضر'
        };
        return texts[status] || status;
    }

    // Refresh dashboard data
    async refresh() {
        await this.loadDashboardStats();
        await this.loadUpcomingAppointments();
        await this.loadRecentPatients();
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (authService.isAuthenticated()) {
        window.dashboardManager = new DashboardManager();
        
        // Auto-refresh every 5 minutes
        setInterval(() => {
            window.dashboardManager.refresh();
        }, 5 * 60 * 1000);
    }
});

// Add icon circle styles
const style = document.createElement('style');
style.textContent = `
    .icon-circle {
        height: 2.5rem;
        width: 2.5rem;
        border-radius: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
`;
document.head.appendChild(style);
