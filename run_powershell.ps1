# PowerShell script to run Dental Clinic Management System
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Dental Clinic Management System" -ForegroundColor Green
Write-Host "   نظام إدارة عيادة الأسنان" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Set Java environment
$env:JAVA_HOME = "C:\Program Files\Java\jdk-24"
$env:PATH = "$env:JAVA_HOME\bin;$env:PATH"

Write-Host "Checking Java installation..." -ForegroundColor Yellow

try {
    $javaVersion = & "$env:JAVA_HOME\bin\java.exe" -version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Java is ready!" -ForegroundColor Green
        Write-Host $javaVersion[0] -ForegroundColor White
    } else {
        throw "Java not working"
    }
} catch {
    Write-Host "❌ Error: Java not found at $env:JAVA_HOME" -ForegroundColor Red
    Write-Host "Please install Java 17+ from https://adoptium.net/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "🚀 Starting Dental Clinic Management System..." -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Application will be available at:" -ForegroundColor Cyan
Write-Host "   - Main page: http://localhost:8080/" -ForegroundColor White
Write-Host "   - Login: http://localhost:8080/login" -ForegroundColor White
Write-Host "   - API docs: http://localhost:8080/api/swagger-ui.html" -ForegroundColor White
Write-Host "   - H2 Database: http://localhost:8080/api/h2-console" -ForegroundColor White
Write-Host ""
Write-Host "🔐 Default credentials:" -ForegroundColor Cyan
Write-Host "   - Admin: admin / admin123" -ForegroundColor White
Write-Host "   - Doctor: doctor / doctor123" -ForegroundColor White
Write-Host "   - Receptionist: receptionist / receptionist123" -ForegroundColor White
Write-Host ""
Write-Host "⏳ Starting application (may take 3-5 minutes first time)..." -ForegroundColor Yellow
Write-Host "🛑 Press Ctrl+C to stop" -ForegroundColor Red
Write-Host ""

# Start the application
try {
    & .\mvnw.cmd clean spring-boot:run
} catch {
    Write-Host ""
    Write-Host "❌ Error starting application: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Possible solutions:" -ForegroundColor Yellow
    Write-Host "   - Check internet connection" -ForegroundColor White
    Write-Host "   - Make sure port 8080 is not in use" -ForegroundColor White
    Write-Host "   - Run as administrator" -ForegroundColor White
}

Write-Host ""
Write-Host "🏁 Application stopped" -ForegroundColor Gray
Read-Host "Press Enter to exit"
