package com.dentalclinic.management.controller;

import com.dentalclinic.management.dto.DoctorDto;
import com.dentalclinic.management.service.DoctorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/doctors")
@RequiredArgsConstructor
@Tag(name = "Doctor Management", description = "APIs for managing doctors")
@CrossOrigin(origins = "*", maxAge = 3600)
public class DoctorController {
    
    private final DoctorService doctorService;
    
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get all available doctors", description = "Retrieves all available doctors")
    public ResponseEntity<List<DoctorDto>> getAllAvailableDoctors() {
        List<DoctorDto> doctors = doctorService.getAllAvailableDoctors();
        return ResponseEntity.ok(doctors);
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get doctor by ID", description = "Retrieves a doctor by their ID")
    public ResponseEntity<?> getDoctorById(
            @Parameter(description = "Doctor ID") @PathVariable Long id) {
        return doctorService.getDoctorById(id)
                .map(doctor -> ResponseEntity.ok().body(doctor))
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR')")
    @Operation(summary = "Get doctor by user ID", description = "Retrieves a doctor by their user ID")
    public ResponseEntity<?> getDoctorByUserId(
            @Parameter(description = "User ID") @PathVariable Long userId) {
        return doctorService.getDoctorByUserId(userId)
                .map(doctor -> ResponseEntity.ok().body(doctor))
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/specialization/{specialization}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get doctors by specialization", description = "Retrieves doctors by their specialization")
    public ResponseEntity<List<DoctorDto>> getDoctorsBySpecialization(
            @Parameter(description = "Specialization") @PathVariable String specialization) {
        
        List<DoctorDto> doctors = doctorService.getDoctorsBySpecialization(specialization);
        return ResponseEntity.ok(doctors);
    }
    
    @GetMapping("/stats/total")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get total doctors count", description = "Returns the total number of available doctors")
    public ResponseEntity<Long> getTotalDoctorsCount() {
        Long count = doctorService.getTotalDoctorsCount();
        return ResponseEntity.ok(count);
    }
}
