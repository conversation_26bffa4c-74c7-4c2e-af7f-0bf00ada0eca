package com.dentalclinic.management.controller;

import com.dentalclinic.management.dto.MedicalRecordCreateRequest;
import com.dentalclinic.management.dto.MedicalRecordDto;
import com.dentalclinic.management.dto.MessageResponse;
import com.dentalclinic.management.service.MedicalRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/medical-records")
@RequiredArgsConstructor
@Tag(name = "Medical Record Management", description = "APIs for managing medical records")
@CrossOrigin(origins = "*", maxAge = 3600)
public class MedicalRecordController {
    
    private final MedicalRecordService medicalRecordService;
    
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR')")
    @Operation(summary = "Create a new medical record", description = "Creates a new medical record")
    public ResponseEntity<?> createMedicalRecord(@Valid @RequestBody MedicalRecordCreateRequest request) {
        try {
            MedicalRecordDto createdRecord = medicalRecordService.createMedicalRecord(request);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdRecord);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get medical record by ID", description = "Retrieves a medical record by its ID")
    public ResponseEntity<?> getMedicalRecordById(
            @Parameter(description = "Medical Record ID") @PathVariable Long id) {
        return medicalRecordService.getMedicalRecordById(id)
                .map(record -> ResponseEntity.ok().body(record))
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/patient/{patientId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get medical records by patient", description = "Retrieves all medical records for a specific patient")
    public ResponseEntity<List<MedicalRecordDto>> getMedicalRecordsByPatient(
            @Parameter(description = "Patient ID") @PathVariable Long patientId) {
        
        List<MedicalRecordDto> records = medicalRecordService.getMedicalRecordsByPatient(patientId);
        return ResponseEntity.ok(records);
    }
    
    @GetMapping("/patient/{patientId}/paginated")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get medical records by patient with pagination", description = "Retrieves medical records for a patient with pagination")
    public ResponseEntity<Page<MedicalRecordDto>> getMedicalRecordsByPatientPaginated(
            @Parameter(description = "Patient ID") @PathVariable Long patientId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<MedicalRecordDto> records = medicalRecordService.getMedicalRecordsByPatient(patientId, pageable);
        return ResponseEntity.ok(records);
    }
    
    @GetMapping("/doctor/{doctorId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR')")
    @Operation(summary = "Get medical records by doctor", description = "Retrieves all medical records for a specific doctor")
    public ResponseEntity<List<MedicalRecordDto>> getMedicalRecordsByDoctor(
            @Parameter(description = "Doctor ID") @PathVariable Long doctorId) {
        
        List<MedicalRecordDto> records = medicalRecordService.getMedicalRecordsByDoctor(doctorId);
        return ResponseEntity.ok(records);
    }
    
    @GetMapping("/doctor/{doctorId}/paginated")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR')")
    @Operation(summary = "Get medical records by doctor with pagination", description = "Retrieves medical records for a doctor with pagination")
    public ResponseEntity<Page<MedicalRecordDto>> getMedicalRecordsByDoctorPaginated(
            @Parameter(description = "Doctor ID") @PathVariable Long doctorId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<MedicalRecordDto> records = medicalRecordService.getMedicalRecordsByDoctor(doctorId, pageable);
        return ResponseEntity.ok(records);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR')")
    @Operation(summary = "Update medical record", description = "Updates an existing medical record")
    public ResponseEntity<?> updateMedicalRecord(
            @Parameter(description = "Medical Record ID") @PathVariable Long id,
            @Valid @RequestBody MedicalRecordCreateRequest request) {
        try {
            MedicalRecordDto updatedRecord = medicalRecordService.updateMedicalRecord(id, request);
            return ResponseEntity.ok(updatedRecord);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }

    @GetMapping("/appointment/{appointmentId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get medical record by appointment", description = "Retrieves medical record for a specific appointment")
    public ResponseEntity<?> getMedicalRecordByAppointment(
            @Parameter(description = "Appointment ID") @PathVariable Long appointmentId) {
        return medicalRecordService.getMedicalRecordByAppointment(appointmentId)
                .map(record -> ResponseEntity.ok().body(record))
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/patient/{patientId}/date-range")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get medical records by date range", description = "Retrieves medical records for a patient within a date range")
    public ResponseEntity<List<MedicalRecordDto>> getMedicalRecordsByDateRange(
            @Parameter(description = "Patient ID") @PathVariable Long patientId,
            @Parameter(description = "Start date") @RequestParam
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date") @RequestParam
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {

        List<MedicalRecordDto> records = medicalRecordService.getMedicalRecordsByDateRange(
                patientId, startDate, endDate);
        return ResponseEntity.ok(records);
    }

    @GetMapping("/stats/patient/{patientId}/count")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get medical records count by patient", description = "Returns the total number of medical records for a patient")
    public ResponseEntity<Long> getMedicalRecordsCountByPatient(
            @Parameter(description = "Patient ID") @PathVariable Long patientId) {

        Long count = medicalRecordService.getMedicalRecordsCountByPatient(patientId);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/stats/doctor/{doctorId}/count")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR')")
    @Operation(summary = "Get medical records count by doctor", description = "Returns the total number of medical records for a doctor")
    public ResponseEntity<Long> getMedicalRecordsCountByDoctor(
            @Parameter(description = "Doctor ID") @PathVariable Long doctorId) {

        Long count = medicalRecordService.getMedicalRecordsCountByDoctor(doctorId);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/stats/today/count")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get today's medical records count", description = "Returns the total number of medical records created today")
    public ResponseEntity<Long> getTodayMedicalRecordsCount() {
        Long count = medicalRecordService.getTodayMedicalRecordsCount();
        return ResponseEntity.ok(count);
    }
}
