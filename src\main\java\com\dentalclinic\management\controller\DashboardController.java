package com.dentalclinic.management.controller;

import com.dentalclinic.management.dto.DashboardStatsDto;
import com.dentalclinic.management.dto.FinancialReportDto;
import com.dentalclinic.management.dto.MedicalReportDto;
import com.dentalclinic.management.service.DashboardService;
import com.dentalclinic.management.service.ReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

@RestController
@RequestMapping("/dashboard")
@RequiredArgsConstructor
@Tag(name = "Dashboard & Reports", description = "APIs for dashboard statistics and reports")
@CrossOrigin(origins = "*", maxAge = 3600)
public class DashboardController {
    
    private final DashboardService dashboardService;
    private final ReportService reportService;
    
    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get dashboard statistics", description = "Retrieves comprehensive dashboard statistics")
    public ResponseEntity<DashboardStatsDto> getDashboardStats() {
        DashboardStatsDto stats = dashboardService.getDashboardStats();
        return ResponseEntity.ok(stats);
    }
    
    @GetMapping("/reports/financial")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Generate financial report", description = "Generates a comprehensive financial report for a date range")
    public ResponseEntity<FinancialReportDto> generateFinancialReport(
            @Parameter(description = "Start date") @RequestParam 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date") @RequestParam 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        FinancialReportDto report = reportService.generateFinancialReport(startDate, endDate);
        return ResponseEntity.ok(report);
    }
    
    @GetMapping("/reports/medical")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR')")
    @Operation(summary = "Generate medical report", description = "Generates a comprehensive medical report for a date range")
    public ResponseEntity<MedicalReportDto> generateMedicalReport(
            @Parameter(description = "Start date") @RequestParam 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date") @RequestParam 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        MedicalReportDto report = reportService.generateMedicalReport(startDate, endDate);
        return ResponseEntity.ok(report);
    }
}
