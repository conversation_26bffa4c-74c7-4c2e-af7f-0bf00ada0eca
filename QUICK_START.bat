@echo off
chcp 65001 >nul
echo ========================================
echo   🦷 نظام إدارة عيادة الأسنان
echo   Dental Clinic Management System
echo ========================================
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Java متوفر - جاري تشغيل التطبيق...
    goto :run_app
)

echo ❌ Java غير مثبت على النظام
echo.
echo 📋 لتشغيل التطبيق، يرجى اتباع الخطوات التالية:
echo.
echo 1️⃣ تحميل وتثبيت Java 17:
echo    🔗 اذهب إلى: https://adoptium.net/
echo    📥 حمل Eclipse Temurin 17 LTS
echo    ⚙️ قم بتثبيته مع تفعيل "Add to PATH"
echo.
echo 2️⃣ أو استخدم الرابط المباشر:
echo    https://github.com/adoptium/temurin17-binaries/releases/download/jdk-********+1/OpenJDK17U-jdk_x64_windows_hotspot_********_1.msi
echo.
echo 3️⃣ بعد التثبيت:
echo    🔄 أعد تشغيل Command Prompt
echo    ▶️ شغل هذا الملف مرة أخرى
echo.

REM Open download page
echo 🌐 فتح صفحة التحميل...
start https://adoptium.net/

echo.
echo ⏳ انتظار تثبيت Java...
echo اضغط أي مفتاح بعد تثبيت Java لمتابعة التشغيل
pause

REM Check again after user confirmation
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java لا يزال غير متاح
    echo يرجى التأكد من تثبيت Java وإعادة تشغيل Command Prompt
    pause
    exit /b 1
)

:run_app
echo.
echo 🚀 بدء تشغيل نظام إدارة عيادة الأسنان...
echo.

java -version
echo.

echo 🌐 سيكون التطبيق متاحاً على الروابط التالية:
echo.
echo    📱 الصفحة الرئيسية:
echo       http://localhost:8080/
echo.
echo    🔐 صفحة تسجيل الدخول:
echo       http://localhost:8080/login
echo.
echo    📚 وثائق API (Swagger):
echo       http://localhost:8080/api/swagger-ui.html
echo.
echo    🗄️ قاعدة البيانات H2:
echo       http://localhost:8080/api/h2-console
echo.
echo 🔑 بيانات تسجيل الدخول الافتراضية:
echo    👨‍💼 مدير النظام: admin / admin123
echo    👨‍⚕️ طبيب: doctor / doctor123
echo    👩‍💼 موظف استقبال: receptionist / receptionist123
echo.
echo 📊 بيانات قاعدة البيانات H2:
echo    🔗 JDBC URL: jdbc:h2:mem:dental_clinic_db
echo    👤 Username: sa
echo    🔒 Password: (فارغ)
echo.
echo ⏳ جاري تحميل التبعيات وتشغيل التطبيق...
echo    (قد يستغرق 2-3 دقائق في المرة الأولى)
echo.
echo 🛑 لإيقاف التطبيق اضغط Ctrl+C
echo.
echo ========================================
echo.

REM Start the application
if exist "mvnw.cmd" (
    echo 🔧 استخدام Maven Wrapper...
    .\mvnw.cmd spring-boot:run
) else (
    echo ❌ Maven Wrapper غير موجود
    echo 🔧 محاولة استخدام Maven العادي...
    mvn spring-boot:run
)

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo.
    echo 🔍 الأسباب المحتملة:
    echo    - مشكلة في الاتصال بالإنترنت
    echo    - المنفذ 8080 مستخدم من تطبيق آخر
    echo    - مشكلة في إعدادات Java
    echo.
    echo 💡 الحلول المقترحة:
    echo    1. تأكد من الاتصال بالإنترنت
    echo    2. أغلق أي تطبيق يستخدم المنفذ 8080
    echo    3. أعد تشغيل Command Prompt كمدير
    echo.
)

echo.
echo 🏁 انتهى تشغيل التطبيق
pause
