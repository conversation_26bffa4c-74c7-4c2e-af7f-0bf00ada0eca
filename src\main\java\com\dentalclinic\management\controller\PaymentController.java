package com.dentalclinic.management.controller;

import com.dentalclinic.management.dto.MessageResponse;
import com.dentalclinic.management.dto.PaymentCreateRequest;
import com.dentalclinic.management.dto.PaymentDto;
import com.dentalclinic.management.entity.PaymentMethod;
import com.dentalclinic.management.service.PaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/payments")
@RequiredArgsConstructor
@Tag(name = "Payment Management", description = "APIs for managing payments")
@CrossOrigin(origins = "*", maxAge = 3600)
public class PaymentController {
    
    private final PaymentService paymentService;
    
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Create a new payment", description = "Creates a new payment for an invoice")
    public ResponseEntity<?> createPayment(@Valid @RequestBody PaymentCreateRequest request) {
        try {
            PaymentDto createdPayment = paymentService.createPayment(request);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdPayment);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }
    
    @GetMapping("/invoice/{invoiceId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get payments by invoice", description = "Retrieves all payments for a specific invoice")
    public ResponseEntity<List<PaymentDto>> getPaymentsByInvoice(
            @Parameter(description = "Invoice ID") @PathVariable Long invoiceId) {
        
        List<PaymentDto> payments = paymentService.getPaymentsByInvoice(invoiceId);
        return ResponseEntity.ok(payments);
    }
    
    @GetMapping("/method/{paymentMethod}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get payments by method", description = "Retrieves all payments by payment method")
    public ResponseEntity<List<PaymentDto>> getPaymentsByMethod(
            @Parameter(description = "Payment Method") @PathVariable PaymentMethod paymentMethod) {
        
        List<PaymentDto> payments = paymentService.getPaymentsByMethod(paymentMethod);
        return ResponseEntity.ok(payments);
    }
    
    @GetMapping("/date-range")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get payments by date range", description = "Retrieves payments within a date range")
    public ResponseEntity<List<PaymentDto>> getPaymentsByDateRange(
            @Parameter(description = "Start date") @RequestParam 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date") @RequestParam 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        List<PaymentDto> payments = paymentService.getPaymentsByDateRange(startDate, endDate);
        return ResponseEntity.ok(payments);
    }
    
    @GetMapping("/today")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get today's payments", description = "Retrieves all payments made today")
    public ResponseEntity<List<PaymentDto>> getTodayPayments() {
        List<PaymentDto> payments = paymentService.getTodayPayments();
        return ResponseEntity.ok(payments);
    }
    
    @GetMapping("/stats/today/count")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get today's payments count", description = "Returns the total number of payments made today")
    public ResponseEntity<Long> getTodayPaymentsCount() {
        Long count = paymentService.getTodayPaymentsCount();
        return ResponseEntity.ok(count);
    }
    
    @GetMapping("/stats/today/total")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get today's payments total", description = "Returns the total amount of payments made today")
    public ResponseEntity<BigDecimal> getTodayPaymentsTotal() {
        BigDecimal total = paymentService.getTodayPaymentsTotal();
        return ResponseEntity.ok(total);
    }
    
    @GetMapping("/stats/total")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get payments total by date range", description = "Returns the total amount of payments for a date range")
    public ResponseEntity<BigDecimal> getPaymentsTotalByDateRange(
            @Parameter(description = "Start date") @RequestParam 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date") @RequestParam 
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        BigDecimal total = paymentService.getPaymentsTotalByDateRange(startDate, endDate);
        return ResponseEntity.ok(total);
    }
}
