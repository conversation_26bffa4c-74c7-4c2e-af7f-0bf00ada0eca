@echo off
echo ========================================
echo   Dental Clinic Management System
echo ========================================
echo.

REM Check if Java is available
java -version >nul 2>&1
if %errorlevel% equ 0 (
    echo Java is available - Starting application...
    goto :run_app
)

echo Java is not installed
echo.
echo Opening Java download page...
start https://adoptium.net/
echo.
echo Please:
echo 1. Download Eclipse Temurin 17 LTS
echo 2. Install it with "Add to PATH" option
echo 3. Restart Command Prompt
echo 4. Run this file again
echo.
pause
exit /b 1

:run_app
echo.
echo Starting Dental Clinic Management System...
echo.

java -version
echo.

echo Application will be available at:
echo   - Main page: http://localhost:8080/
echo   - Login: http://localhost:8080/login
echo   - API docs: http://localhost:8080/api/swagger-ui.html
echo   - H2 Database: http://localhost:8080/api/h2-console
echo.
echo Default login credentials:
echo   - Admin: admin / admin123
echo   - Doctor: doctor / doctor123
echo   - Receptionist: receptionist / receptionist123
echo.
echo Starting application...
echo (First run may take 3-5 minutes)
echo.
echo Press Ctrl+C to stop the application
echo.

REM Start the application
if exist "mvnw.cmd" (
    echo Using Maven Wrapper...
    call mvnw.cmd spring-boot:run
) else (
    echo Maven Wrapper not found
    echo Trying system Maven...
    mvn spring-boot:run
)

echo.
echo Application stopped
pause
