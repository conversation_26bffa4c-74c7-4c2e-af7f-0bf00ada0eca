package com.dentalclinic.management.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class WebController {
    
    @GetMapping("/")
    public String index() {
        return "redirect:/login";
    }
    
    @GetMapping("/login")
    public String login() {
        return "login";
    }
    
    @GetMapping("/dashboard")
    public String dashboard() {
        return "dashboard";
    }
    
    @GetMapping("/patients")
    public String patients() {
        return "patients";
    }
    
    @GetMapping("/appointments")
    public String appointments() {
        return "appointments";
    }
    
    @GetMapping("/medical-records")
    public String medicalRecords() {
        return "medical-records";
    }
    
    @GetMapping("/invoices")
    public String invoices() {
        return "invoices";
    }
    
    @GetMapping("/reports")
    public String reports() {
        return "reports";
    }
}
