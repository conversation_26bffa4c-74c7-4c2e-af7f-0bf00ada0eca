@echo off
echo ========================================
echo   Dental Clinic Management System
echo   نظام إدارة عيادة الأسنان
echo ========================================
echo.

REM Set JAVA_HOME to the detected Java 24
set "JAVA_HOME=C:\Program Files\Java\jdk-24"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo Found Java 24 - Setting up environment...
echo JAVA_HOME: %JAVA_HOME%
echo.

REM Verify Java is working
"%JAVA_HOME%\bin\java.exe" -version
if %errorlevel% neq 0 (
    echo Error: Java is not working properly
    pause
    exit /b 1
)

echo.
echo ✅ Java 24 is ready!
echo.
echo 🚀 Starting Dental Clinic Management System...
echo.
echo 🌐 Application will be available at:
echo    - Main page: http://localhost:8080/
echo    - Login: http://localhost:8080/login
echo    - API docs: http://localhost:8080/api/swagger-ui.html
echo    - H2 Database: http://localhost:8080/api/h2-console
echo.
echo 🔐 Default login credentials:
echo    - Admin: admin / admin123
echo    - Doctor: doctor / doctor123
echo    - Receptionist: receptionist / receptionist123
echo.
echo 📊 H2 Database connection:
echo    - JDBC URL: jdbc:h2:mem:dental_clinic_db
echo    - Username: sa
echo    - Password: (empty)
echo.
echo ⏳ Starting application...
echo    (First run may take 3-5 minutes to download dependencies)
echo.
echo 🛑 Press Ctrl+C to stop the application
echo.

REM Start the application using Maven Wrapper
if exist "mvnw.cmd" (
    echo Using Maven Wrapper...
    call mvnw.cmd spring-boot:run
) else (
    echo Maven Wrapper not found, trying system Maven...
    mvn spring-boot:run
)

if %errorlevel% neq 0 (
    echo.
    echo ❌ Application failed to start
    echo.
    echo Possible causes:
    echo - Internet connection required for first run
    echo - Port 8080 might be in use
    echo - Firewall blocking the application
    echo.
    echo Solutions:
    echo - Check internet connection
    echo - Close any application using port 8080
    echo - Run as administrator
    echo.
)

echo.
echo 🏁 Application stopped
pause
