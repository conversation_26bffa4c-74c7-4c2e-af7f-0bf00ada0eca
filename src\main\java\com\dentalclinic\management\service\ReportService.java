package com.dentalclinic.management.service;

import com.dentalclinic.management.dto.*;
import com.dentalclinic.management.entity.AppointmentStatus;
import com.dentalclinic.management.entity.InvoiceStatus;
import com.dentalclinic.management.entity.PaymentMethod;
import com.dentalclinic.management.repository.PaymentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class ReportService {
    
    private final InvoiceService invoiceService;
    private final PaymentService paymentService;
    private final PaymentRepository paymentRepository;
    private final PatientService patientService;
    private final AppointmentService appointmentService;
    private final MedicalRecordService medicalRecordService;
    
    public FinancialReportDto generateFinancialReport(LocalDateTime startDate, LocalDateTime endDate) {
        log.info("Generating financial report from {} to {}", startDate, endDate);
        
        FinancialReportDto report = new FinancialReportDto();
        report.setStartDate(startDate);
        report.setEndDate(endDate);
        
        // Revenue Statistics
        report.setTotalRevenue(invoiceService.getTotalRevenueByDateRange(startDate, endDate));
        report.setTotalPaidAmount(invoiceService.getPaidAmountByDateRange(startDate, endDate));
        report.setTotalPendingAmount(report.getTotalRevenue().subtract(report.getTotalPaidAmount()));
        
        // Invoice Statistics
        // Note: These would need additional repository methods for date range filtering
        report.setTotalInvoices(0L); // Placeholder
        report.setPaidInvoices(0L);   // Placeholder
        report.setPendingInvoices(0L); // Placeholder
        report.setOverdueInvoices(0L); // Placeholder
        report.setCancelledInvoices(0L); // Placeholder
        
        // Payment Statistics
        report.setTotalPaymentsAmount(paymentService.getPaymentsTotalByDateRange(startDate, endDate));
        
        // Payment by method statistics
        List<PaymentMethodStatsDto> paymentsByMethod = generatePaymentMethodStats(startDate, endDate);
        report.setPaymentsByMethod(paymentsByMethod);
        
        log.info("Financial report generated successfully");
        return report;
    }
    
    public MedicalReportDto generateMedicalReport(LocalDateTime startDate, LocalDateTime endDate) {
        log.info("Generating medical report from {} to {}", startDate, endDate);
        
        MedicalReportDto report = new MedicalReportDto();
        report.setStartDate(startDate);
        report.setEndDate(endDate);
        
        // Patient Statistics
        report.setTotalPatients(patientService.getTotalPatientsCount());
        // Note: These would need additional methods for date range filtering
        report.setNewPatients(0L); // Placeholder
        report.setActivePatients(patientService.getTotalPatientsCount());
        
        // Appointment Statistics
        // Note: These would need additional repository methods for date range filtering
        report.setTotalAppointments(0L); // Placeholder
        report.setCompletedAppointments(0L); // Placeholder
        report.setCancelledAppointments(0L); // Placeholder
        report.setNoShowAppointments(0L); // Placeholder
        
        // Medical Record Statistics
        // Note: This would need additional methods for date range filtering
        report.setTotalMedicalRecords(0L); // Placeholder
        
        // Doctor Statistics - Placeholder
        report.setDoctorStats(new ArrayList<>());
        
        // Treatment Statistics - Placeholder
        report.setTreatmentStats(new ArrayList<>());
        
        log.info("Medical report generated successfully");
        return report;
    }
    
    private List<PaymentMethodStatsDto> generatePaymentMethodStats(LocalDateTime startDate, LocalDateTime endDate) {
        List<PaymentMethodStatsDto> stats = new ArrayList<>();
        
        // Get payment data by method
        List<Object[]> paymentData = paymentRepository.getPaymentsByMethodAndDateRange(startDate, endDate);
        
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (Object[] data : paymentData) {
            BigDecimal amount = (BigDecimal) data[1];
            totalAmount = totalAmount.add(amount);
        }
        
        for (Object[] data : paymentData) {
            PaymentMethod method = (PaymentMethod) data[0];
            BigDecimal amount = (BigDecimal) data[1];
            
            BigDecimal percentage = totalAmount.compareTo(BigDecimal.ZERO) > 0 ? 
                    amount.divide(totalAmount, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)) : 
                    BigDecimal.ZERO;
            
            PaymentMethodStatsDto stat = new PaymentMethodStatsDto();
            stat.setPaymentMethod(method);
            stat.setTotalAmount(amount);
            stat.setPercentage(percentage);
            // Note: Transaction count would need additional query
            stat.setTransactionCount(0L);
            
            stats.add(stat);
        }
        
        return stats;
    }
}
