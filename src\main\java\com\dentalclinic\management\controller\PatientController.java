package com.dentalclinic.management.controller;

import com.dentalclinic.management.dto.MessageResponse;
import com.dentalclinic.management.dto.PatientCreateRequest;
import com.dentalclinic.management.dto.PatientDto;
import com.dentalclinic.management.service.PatientService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/patients")
@RequiredArgsConstructor
@Tag(name = "Patient Management", description = "APIs for managing patients")
@CrossOrigin(origins = "*", maxAge = 3600)
public class PatientController {
    
    private final PatientService patientService;
    
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Create a new patient", description = "Creates a new patient record")
    public ResponseEntity<?> createPatient(@Valid @RequestBody PatientCreateRequest request) {
        try {
            PatientDto createdPatient = patientService.createPatient(request);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdPatient);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get patient by ID", description = "Retrieves a patient by their ID")
    public ResponseEntity<?> getPatientById(
            @Parameter(description = "Patient ID") @PathVariable Long id) {
        return patientService.getPatientById(id)
                .map(patient -> ResponseEntity.ok().body(patient))
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get all patients", description = "Retrieves all active patients with pagination")
    public ResponseEntity<Page<PatientDto>> getAllPatients(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<PatientDto> patients = patientService.getAllPatients(pageable);
        return ResponseEntity.ok(patients);
    }
    
    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Search patients", description = "Search patients by name, phone, email, or national ID")
    public ResponseEntity<Page<PatientDto>> searchPatients(
            @Parameter(description = "Search term") @RequestParam String searchTerm,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<PatientDto> patients = patientService.searchPatients(searchTerm, pageable);
        return ResponseEntity.ok(patients);
    }
    
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Update patient", description = "Updates an existing patient record")
    public ResponseEntity<?> updatePatient(
            @Parameter(description = "Patient ID") @PathVariable Long id,
            @Valid @RequestBody PatientCreateRequest request) {
        try {
            PatientDto updatedPatient = patientService.updatePatient(id, request);
            return ResponseEntity.ok(updatedPatient);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Delete patient", description = "Soft deletes a patient record")
    public ResponseEntity<?> deletePatient(
            @Parameter(description = "Patient ID") @PathVariable Long id) {
        try {
            patientService.deletePatient(id);
            return ResponseEntity.ok(new MessageResponse("Patient deleted successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }
    
    @GetMapping("/stats/total")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get total patients count", description = "Returns the total number of active patients")
    public ResponseEntity<Long> getTotalPatientsCount() {
        Long count = patientService.getTotalPatientsCount();
        return ResponseEntity.ok(count);
    }
    
    @GetMapping("/stats/today")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get today's registrations", description = "Returns the number of patients registered today")
    public ResponseEntity<Long> getTodayRegistrationsCount() {
        Long count = patientService.getTodayRegistrationsCount();
        return ResponseEntity.ok(count);
    }
}
