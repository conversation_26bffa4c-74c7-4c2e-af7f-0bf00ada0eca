package com.dentalclinic.management.dto;

import com.dentalclinic.management.entity.Gender;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class PatientDto {
    
    private Long id;
    
    @NotBlank
    @Size(max = 50)
    private String firstName;
    
    @NotBlank
    @Size(max = 50)
    private String lastName;
    
    private LocalDate dateOfBirth;
    
    private Gender gender;
    
    @Size(max = 15)
    private String phoneNumber;
    
    @Email
    @Size(max = 100)
    private String email;
    
    @Size(max = 200)
    private String address;
    
    @Size(max = 20)
    private String nationalId;
    
    @Size(max = 50)
    private String emergencyContactName;
    
    @Size(max = 15)
    private String emergencyContactPhone;
    
    @Size(max = 100)
    private String insuranceProvider;
    
    @Size(max = 50)
    private String insuranceNumber;
    
    private String allergies;
    
    private String medicalHistory;
    
    private String notes;
    
    private Boolean isActive;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    // Computed fields
    private String fullName;
    private Integer age;
}
