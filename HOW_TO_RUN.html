<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كيفية تشغيل نظام إدارة عيادة الأسنان</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        h1 {
            color: #4e73df;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #5a5c69;
            border-bottom: 2px solid #e3e6f0;
            padding-bottom: 10px;
        }
        .step {
            background: #f8f9fc;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #4e73df;
        }
        .step h3 {
            margin-top: 0;
            color: #4e73df;
        }
        .code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .download-btn {
            display: inline-block;
            background: #4e73df;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        .download-btn:hover {
            background: #2e59d9;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        ul {
            padding-right: 20px;
        }
        li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🦷 كيفية تشغيل نظام إدارة عيادة الأسنان</h1>
        
        <div class="warning">
            <strong>⚠️ تنبيه:</strong> يجب تثبيت Java 17 أو أحدث لتشغيل التطبيق
        </div>

        <h2>📋 الخطوات المطلوبة:</h2>

        <div class="step">
            <h3>1️⃣ تثبيت Java 17</h3>
            <p><strong>الطريقة الأولى (موصى بها):</strong></p>
            <ul>
                <li>اذهب إلى: <a href="https://adoptium.net/" target="_blank">https://adoptium.net/</a></li>
                <li>حمل Eclipse Temurin 17 LTS للويندوز</li>
                <li>قم بتثبيته مع تفعيل خيار "Add to PATH"</li>
            </ul>
            
            <p><strong>رابط التحميل المباشر:</strong></p>
            <a href="https://github.com/adoptium/temurin17-binaries/releases/download/jdk-********+1/OpenJDK17U-jdk_x64_windows_hotspot_********_1.msi" 
               class="download-btn" target="_blank">تحميل Java 17 مباشرة</a>
        </div>

        <div class="step">
            <h3>2️⃣ التحقق من تثبيت Java</h3>
            <p>افتح Command Prompt وشغل الأمر التالي:</p>
            <div class="code">java -version</div>
            <p>يجب أن ترى رسالة تحتوي على "17" في رقم الإصدار</p>
        </div>

        <div class="step">
            <h3>3️⃣ تشغيل التطبيق</h3>
            <p>اذهب إلى مجلد المشروع وشغل أحد الملفات التالية:</p>
            
            <p><strong>الطريقة الأولى (الأسهل):</strong></p>
            <div class="code">start.bat</div>
            
            <p><strong>الطريقة الثانية:</strong></p>
            <div class="code">run.bat</div>
            
            <p><strong>الطريقة الثالثة (يدوياً):</strong></p>
            <div class="code">.\mvnw.cmd spring-boot:run</div>
        </div>

        <div class="success">
            <h3>🎉 بعد تشغيل التطبيق بنجاح:</h3>
            <ul>
                <li><strong>الصفحة الرئيسية:</strong> <a href="http://localhost:8080/" target="_blank">http://localhost:8080/</a></li>
                <li><strong>تسجيل الدخول:</strong> <a href="http://localhost:8080/login" target="_blank">http://localhost:8080/login</a></li>
                <li><strong>Swagger API:</strong> <a href="http://localhost:8080/api/swagger-ui.html" target="_blank">http://localhost:8080/api/swagger-ui.html</a></li>
                <li><strong>قاعدة البيانات H2:</strong> <a href="http://localhost:8080/api/h2-console" target="_blank">http://localhost:8080/api/h2-console</a></li>
            </ul>
        </div>

        <div class="info">
            <h3>🔐 بيانات تسجيل الدخول الافتراضية:</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr style="background: #f8f9fc;">
                    <th style="padding: 10px; border: 1px solid #dee2e6;">الدور</th>
                    <th style="padding: 10px; border: 1px solid #dee2e6;">اسم المستخدم</th>
                    <th style="padding: 10px; border: 1px solid #dee2e6;">كلمة المرور</th>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">مدير النظام</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">admin</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">admin123</td>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">طبيب</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">doctor</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">doctor123</td>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">موظف استقبال</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">receptionist</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">receptionist123</td>
                </tr>
            </table>
        </div>

        <div class="info">
            <h3>📊 بيانات قاعدة البيانات H2:</h3>
            <ul>
                <li><strong>JDBC URL:</strong> jdbc:h2:mem:dental_clinic_db</li>
                <li><strong>Username:</strong> sa</li>
                <li><strong>Password:</strong> (فارغ)</li>
            </ul>
        </div>

        <h2>🛠️ استكشاف الأخطاء:</h2>

        <div class="step">
            <h3>❌ خطأ: "JAVA_HOME not found"</h3>
            <p>الحل: تأكد من تثبيت Java وإضافته إلى PATH</p>
        </div>

        <div class="step">
            <h3>❌ خطأ: "Port 8080 already in use"</h3>
            <p>الحل: أغلق أي تطبيق يستخدم المنفذ 8080 أو غير المنفذ في application.yml</p>
        </div>

        <div class="step">
            <h3>❌ التطبيق لا يبدأ</h3>
            <p>تحقق من ملف logs/dental-clinic.log للأخطاء</p>
        </div>

        <div class="success">
            <h3>✅ نصائح مهمة:</h3>
            <ul>
                <li>التشغيل الأول قد يستغرق بضع دقائق لتحميل التبعيات</li>
                <li>تأكد من اتصال الإنترنت لتحميل Maven dependencies</li>
                <li>لإيقاف التطبيق اضغط Ctrl+C في Command Prompt</li>
                <li>البيانات محفوظة في ذاكرة H2 وستختفي عند إيقاف التطبيق</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #6c757d;">
            <p>🦷 نظام إدارة عيادة الأسنان - تم التطوير بـ Spring Boot</p>
        </div>
    </div>
</body>
</html>
