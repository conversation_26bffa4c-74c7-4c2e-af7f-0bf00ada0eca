package com.dentalclinic.management.repository;

import com.dentalclinic.management.entity.MedicalRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface MedicalRecordRepository extends JpaRepository<MedicalRecord, Long> {
    
    List<MedicalRecord> findByPatientId(Long patientId);
    
    Page<MedicalRecord> findByPatientId(Long patientId, Pageable pageable);
    
    List<MedicalRecord> findByDoctorId(Long doctorId);
    
    Page<MedicalRecord> findByDoctorId(Long doctorId, Pageable pageable);
    
    Optional<MedicalRecord> findByAppointmentId(Long appointmentId);
    
    @Query("SELECT mr FROM MedicalRecord mr WHERE mr.patient.id = :patientId " +
           "ORDER BY mr.createdAt DESC")
    List<MedicalRecord> findByPatientIdOrderByCreatedAtDesc(@Param("patientId") Long patientId);
    
    @Query("SELECT mr FROM MedicalRecord mr WHERE mr.doctor.id = :doctorId " +
           "AND mr.createdAt BETWEEN :startDate AND :endDate")
    List<MedicalRecord> findByDoctorAndDateRange(@Param("doctorId") Long doctorId,
                                                @Param("startDate") LocalDateTime startDate,
                                                @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT mr FROM MedicalRecord mr WHERE mr.patient.id = :patientId " +
           "AND mr.createdAt BETWEEN :startDate AND :endDate")
    List<MedicalRecord> findByPatientAndDateRange(@Param("patientId") Long patientId,
                                                 @Param("startDate") LocalDateTime startDate,
                                                 @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT mr FROM MedicalRecord mr JOIN FETCH mr.patient JOIN FETCH mr.doctor d JOIN FETCH d.user " +
           "WHERE mr.id = :id")
    Optional<MedicalRecord> findByIdWithDetails(@Param("id") Long id);
    
    @Query("SELECT COUNT(mr) FROM MedicalRecord mr WHERE mr.doctor.id = :doctorId")
    Long countByDoctorId(@Param("doctorId") Long doctorId);
    
    @Query("SELECT COUNT(mr) FROM MedicalRecord mr WHERE mr.patient.id = :patientId")
    Long countByPatientId(@Param("patientId") Long patientId);
    
    @Query("SELECT COUNT(mr) FROM MedicalRecord mr WHERE DATE(mr.createdAt) = CURRENT_DATE")
    Long countTodayRecords();
}
