package com.dentalclinic.management.dto;

import com.dentalclinic.management.entity.PaymentMethod;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentMethodStatsDto {
    
    private PaymentMethod paymentMethod;
    private BigDecimal totalAmount;
    private Long transactionCount;
    private BigDecimal percentage;
}
