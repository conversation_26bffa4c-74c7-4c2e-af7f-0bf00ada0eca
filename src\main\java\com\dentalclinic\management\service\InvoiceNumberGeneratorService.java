package com.dentalclinic.management.service;

import com.dentalclinic.management.repository.InvoiceRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
@RequiredArgsConstructor
public class InvoiceNumberGeneratorService {
    
    private final InvoiceRepository invoiceRepository;
    
    public String generateInvoiceNumber() {
        String prefix = "INV";
        String dateFormat = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        String baseNumber = prefix + "-" + dateFormat + "-";
        
        // Find the next sequential number for today
        int sequenceNumber = 1;
        String invoiceNumber;
        
        do {
            invoiceNumber = baseNumber + String.format("%04d", sequenceNumber);
            sequenceNumber++;
        } while (invoiceRepository.existsByInvoiceNumber(invoiceNumber));
        
        return invoiceNumber;
    }
}
