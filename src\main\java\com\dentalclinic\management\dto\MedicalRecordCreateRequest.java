package com.dentalclinic.management.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class MedicalRecordCreateRequest {
    
    @NotNull(message = "Patient ID is required")
    private Long patientId;
    
    @NotNull(message = "Doctor ID is required")
    private Long doctorId;
    
    private Long appointmentId;
    
    private String symptoms;
    
    private String diagnosis;
    
    private String treatment;
    
    private String prescription;
    
    private String followUpInstructions;
    
    private String notes;
}
