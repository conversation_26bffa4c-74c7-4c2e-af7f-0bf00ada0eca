#!/bin/bash

echo "Starting Dental Clinic Management System..."
echo

# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo "Error: Java is not installed or not in PATH"
    echo "Please install Java 17 or higher"
    exit 1
fi

# Check if <PERSON><PERSON> is installed
if ! command -v mvn &> /dev/null; then
    echo "Error: <PERSON><PERSON> is not installed or not in PATH"
    echo "Please install Maven 3.6 or higher"
    exit 1
fi

echo "Java and Maven are available"
echo

# Clean and compile the project
echo "Cleaning and compiling the project..."
mvn clean compile

if [ $? -ne 0 ]; then
    echo "Error: Failed to compile the project"
    exit 1
fi

echo
echo "Starting the application..."
echo
echo "The application will be available at:"
echo "- Main Application: http://localhost:8080/api"
echo "- Swagger UI: http://localhost:8080/api/swagger-ui.html"
echo "- H2 Database Console: http://localhost:8080/api/h2-console (if using H2)"
echo
echo "Default login credentials:"
echo "- Admin: username=admin, password=admin123"
echo "- Doctor: username=doctor, password=doctor123"
echo "- Receptionist: username=receptionist, password=receptionist123"
echo
echo "Press Ctrl+C to stop the application"
echo

# Start the Spring Boot application
mvn spring-boot:run
