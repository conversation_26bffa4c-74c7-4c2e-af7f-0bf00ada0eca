@echo off
echo Starting Dental Clinic Management System...
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH
    echo Please install Java 17 or higher
    pause
    exit /b 1
)

REM Check if <PERSON><PERSON> Wrapper exists, if not try system Maven
if exist "mvnw.cmd" (
    echo Using Maven Wrapper
    set MVN_CMD=mvnw.cmd
) else (
    echo Checking for system Maven...
    mvn -version >nul 2>&1
    if %errorlevel% neq 0 (
        echo Error: <PERSON>ven is not installed or not in PATH
        echo Please install Maven 3.6 or higher, or use <PERSON><PERSON> Wrapper
        pause
        exit /b 1
    )
    set MVN_CMD=mvn
)

echo Java is available and <PERSON><PERSON> is ready
echo.

REM Clean and compile the project
echo Cleaning and compiling the project...
call %MVN_CMD% clean compile

if %errorlevel% neq 0 (
    echo Error: Failed to compile the project
    pause
    exit /b 1
)

echo.
echo Starting the application...
echo.
echo The application will be available at:
echo - Main Application: http://localhost:8080/api
echo - Swagger UI: http://localhost:8080/api/swagger-ui.html
echo - H2 Database Console: http://localhost:8080/api/h2-console (if using H2)
echo.
echo Default login credentials:
echo - Admin: username=admin, password=admin123
echo - Doctor: username=doctor, password=doctor123
echo - Receptionist: username=receptionist, password=receptionist123
echo.
echo Press Ctrl+C to stop the application
echo.

REM Start the Spring Boot application
call %MVN_CMD% spring-boot:run

pause
