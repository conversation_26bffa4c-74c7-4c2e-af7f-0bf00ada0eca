package com.dentalclinic.management.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class FinancialReportDto {
    
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    
    // Revenue Statistics
    private BigDecimal totalRevenue;
    private BigDecimal totalPaidAmount;
    private BigDecimal totalPendingAmount;
    private BigDecimal totalOverdueAmount;
    
    // Invoice Statistics
    private Long totalInvoices;
    private Long paidInvoices;
    private Long pendingInvoices;
    private Long overdueInvoices;
    private Long cancelledInvoices;
    
    // Payment Statistics
    private Long totalPayments;
    private BigDecimal totalPaymentsAmount;
    private List<PaymentMethodStatsDto> paymentsByMethod;
    
    // Daily breakdown
    private List<DailyRevenueDto> dailyRevenue;
}
