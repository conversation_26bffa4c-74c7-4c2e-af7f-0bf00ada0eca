package com.dentalclinic.management.service;

import com.dentalclinic.management.dto.*;
import com.dentalclinic.management.entity.*;
import com.dentalclinic.management.repository.AppointmentRepository;
import com.dentalclinic.management.repository.InvoiceRepository;
import com.dentalclinic.management.repository.PatientRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class InvoiceService {
    
    private final InvoiceRepository invoiceRepository;
    private final PatientRepository patientRepository;
    private final AppointmentRepository appointmentRepository;
    private final InvoiceNumberGeneratorService invoiceNumberGenerator;
    
    public InvoiceDto createInvoice(InvoiceCreateRequest request) {
        log.info("Creating invoice for patient ID: {}", request.getPatientId());
        
        // Validate patient exists
        Patient patient = patientRepository.findByIdAndIsActiveTrue(request.getPatientId())
                .orElseThrow(() -> new RuntimeException("Patient not found with ID: " + request.getPatientId()));
        
        // Validate appointment if provided
        Appointment appointment = null;
        if (request.getAppointmentId() != null) {
            appointment = appointmentRepository.findById(request.getAppointmentId())
                    .orElseThrow(() -> new RuntimeException("Appointment not found with ID: " + request.getAppointmentId()));
            
            // Check if invoice already exists for this appointment
            if (invoiceRepository.findByAppointmentId(request.getAppointmentId()).isPresent()) {
                throw new RuntimeException("Invoice already exists for this appointment");
            }
        }
        
        // Generate invoice number
        String invoiceNumber = invoiceNumberGenerator.generateInvoiceNumber();
        
        // Create invoice
        Invoice invoice = mapToEntity(request, patient, appointment, invoiceNumber);
        
        // Calculate totals
        calculateInvoiceTotals(invoice);
        
        Invoice savedInvoice = invoiceRepository.save(invoice);
        log.info("Invoice created successfully with number: {}", savedInvoice.getInvoiceNumber());
        
        return mapToDto(savedInvoice);
    }
    
    @Transactional(readOnly = true)
    public Optional<InvoiceDto> getInvoiceById(Long id) {
        log.debug("Fetching invoice with ID: {}", id);
        return invoiceRepository.findByIdWithDetails(id)
                .map(this::mapToDto);
    }
    
    @Transactional(readOnly = true)
    public Optional<InvoiceDto> getInvoiceByNumber(String invoiceNumber) {
        log.debug("Fetching invoice with number: {}", invoiceNumber);
        return invoiceRepository.findByInvoiceNumber(invoiceNumber)
                .map(this::mapToDto);
    }
    
    @Transactional(readOnly = true)
    public List<InvoiceDto> getInvoicesByPatient(Long patientId) {
        log.debug("Fetching invoices for patient ID: {}", patientId);
        return invoiceRepository.findByPatientId(patientId)
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public Page<InvoiceDto> getInvoicesByPatient(Long patientId, Pageable pageable) {
        log.debug("Fetching invoices for patient ID: {} with pagination", patientId);
        return invoiceRepository.findByPatientId(patientId, pageable)
                .map(this::mapToDto);
    }
    
    @Transactional(readOnly = true)
    public List<InvoiceDto> getInvoicesByStatus(InvoiceStatus status) {
        log.debug("Fetching invoices with status: {}", status);
        return invoiceRepository.findByStatus(status)
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public Page<InvoiceDto> getInvoicesByStatus(InvoiceStatus status, Pageable pageable) {
        log.debug("Fetching invoices with status: {} with pagination", status);
        return invoiceRepository.findByStatus(status, pageable)
                .map(this::mapToDto);
    }

    public InvoiceDto updateInvoiceStatus(Long id, InvoiceStatus status) {
        log.info("Updating invoice ID: {} to status: {}", id, status);

        Invoice invoice = invoiceRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Invoice not found with ID: " + id));

        invoice.setStatus(status);
        Invoice updatedInvoice = invoiceRepository.save(invoice);

        log.info("Invoice status updated successfully");
        return mapToDto(updatedInvoice);
    }

    @Transactional(readOnly = true)
    public List<InvoiceDto> getOverdueInvoices() {
        log.debug("Fetching overdue invoices");
        return invoiceRepository.findOverdueInvoices()
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<InvoiceDto> getTodayInvoices() {
        log.debug("Fetching today's invoices");
        return invoiceRepository.findTodayInvoices()
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public Long getTodayInvoicesCount() {
        return invoiceRepository.countTodayInvoices();
    }

    @Transactional(readOnly = true)
    public Long getInvoicesCountByStatus(InvoiceStatus status) {
        return invoiceRepository.countByStatus(status);
    }

    @Transactional(readOnly = true)
    public BigDecimal getTodayTotalRevenue() {
        BigDecimal revenue = invoiceRepository.getTodayTotalRevenue();
        return revenue != null ? revenue : BigDecimal.ZERO;
    }

    @Transactional(readOnly = true)
    public BigDecimal getTodayPaidAmount() {
        BigDecimal paidAmount = invoiceRepository.getTodayPaidAmount();
        return paidAmount != null ? paidAmount : BigDecimal.ZERO;
    }

    @Transactional(readOnly = true)
    public BigDecimal getTotalRevenueByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        BigDecimal revenue = invoiceRepository.getTotalRevenueByDateRange(startDate, endDate);
        return revenue != null ? revenue : BigDecimal.ZERO;
    }

    @Transactional(readOnly = true)
    public BigDecimal getPaidAmountByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        BigDecimal paidAmount = invoiceRepository.getPaidAmountByDateRange(startDate, endDate);
        return paidAmount != null ? paidAmount : BigDecimal.ZERO;
    }

    private Invoice mapToEntity(InvoiceCreateRequest request, Patient patient,
                               Appointment appointment, String invoiceNumber) {
        Invoice invoice = new Invoice();
        invoice.setInvoiceNumber(invoiceNumber);
        invoice.setPatient(patient);
        invoice.setAppointment(appointment);
        invoice.setInvoiceDate(LocalDateTime.now());
        invoice.setDueDate(request.getDueDate());
        invoice.setTaxAmount(request.getTaxAmount());
        invoice.setDiscountAmount(request.getDiscountAmount());
        invoice.setNotes(request.getNotes());
        invoice.setStatus(InvoiceStatus.PENDING);

        // Create invoice items
        List<InvoiceItem> invoiceItems = request.getInvoiceItems().stream()
                .map(itemRequest -> mapToInvoiceItem(itemRequest, invoice))
                .collect(Collectors.toList());
        invoice.setInvoiceItems(invoiceItems);

        return invoice;
    }

    private InvoiceItem mapToInvoiceItem(InvoiceItemCreateRequest request, Invoice invoice) {
        InvoiceItem item = new InvoiceItem();
        item.setInvoice(invoice);
        item.setDescription(request.getDescription());
        item.setQuantity(request.getQuantity());
        item.setUnitPrice(request.getUnitPrice());
        // Total price will be calculated automatically in the entity
        return item;
    }

    private void calculateInvoiceTotals(Invoice invoice) {
        BigDecimal subtotal = invoice.getInvoiceItems().stream()
                .map(InvoiceItem::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        invoice.setSubtotal(subtotal);

        BigDecimal totalAmount = subtotal
                .add(invoice.getTaxAmount())
                .subtract(invoice.getDiscountAmount());

        invoice.setTotalAmount(totalAmount);
    }

    private InvoiceDto mapToDto(Invoice invoice) {
        InvoiceDto dto = new InvoiceDto();
        dto.setId(invoice.getId());
        dto.setInvoiceNumber(invoice.getInvoiceNumber());
        dto.setPatientId(invoice.getPatient().getId());
        dto.setPatientName(invoice.getPatient().getFullName());
        dto.setPatientPhone(invoice.getPatient().getPhoneNumber());

        if (invoice.getAppointment() != null) {
            dto.setAppointmentId(invoice.getAppointment().getId());
            dto.setAppointmentDateTime(invoice.getAppointment().getAppointmentDateTime());
        }

        dto.setInvoiceDate(invoice.getInvoiceDate());
        dto.setDueDate(invoice.getDueDate());
        dto.setSubtotal(invoice.getSubtotal());
        dto.setTaxAmount(invoice.getTaxAmount());
        dto.setDiscountAmount(invoice.getDiscountAmount());
        dto.setTotalAmount(invoice.getTotalAmount());
        dto.setPaidAmount(invoice.getPaidAmount());
        dto.setRemainingAmount(invoice.getRemainingAmount());
        dto.setStatus(invoice.getStatus());
        dto.setNotes(invoice.getNotes());
        dto.setCreatedAt(invoice.getCreatedAt());
        dto.setUpdatedAt(invoice.getUpdatedAt());
        dto.setIsFullyPaid(invoice.isFullyPaid());

        // Map invoice items
        if (invoice.getInvoiceItems() != null) {
            List<InvoiceItemDto> itemDtos = invoice.getInvoiceItems().stream()
                    .map(this::mapInvoiceItemToDto)
                    .collect(Collectors.toList());
            dto.setInvoiceItems(itemDtos);
        }

        // Map payments
        if (invoice.getPayments() != null) {
            List<PaymentDto> paymentDtos = invoice.getPayments().stream()
                    .map(this::mapPaymentToDto)
                    .collect(Collectors.toList());
            dto.setPayments(paymentDtos);
        }

        return dto;
    }

    private InvoiceItemDto mapInvoiceItemToDto(InvoiceItem item) {
        InvoiceItemDto dto = new InvoiceItemDto();
        dto.setId(item.getId());
        dto.setInvoiceId(item.getInvoice().getId());
        dto.setDescription(item.getDescription());
        dto.setQuantity(item.getQuantity());
        dto.setUnitPrice(item.getUnitPrice());
        dto.setTotalPrice(item.getTotalPrice());
        return dto;
    }

    private PaymentDto mapPaymentToDto(Payment payment) {
        PaymentDto dto = new PaymentDto();
        dto.setId(payment.getId());
        dto.setInvoiceId(payment.getInvoice().getId());
        dto.setInvoiceNumber(payment.getInvoice().getInvoiceNumber());
        dto.setPaymentAmount(payment.getPaymentAmount());
        dto.setPaymentMethod(payment.getPaymentMethod());
        dto.setPaymentDate(payment.getPaymentDate());
        dto.setTransactionReference(payment.getTransactionReference());
        dto.setNotes(payment.getNotes());
        dto.setCreatedAt(payment.getCreatedAt());
        return dto;
    }
}
