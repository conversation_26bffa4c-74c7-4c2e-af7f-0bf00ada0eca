# نظام إدارة عيادة الأسنان - Dental Clinic Management System

## نظرة عامة
نظام ويب متكامل لإدارة عيادة الأسنان مطور باستخدام Java و Spring Boot، يوفر إدارة شاملة للمرضى، المواعيد، الأطباء، الفواتير، والسجلات الطبية.

## المميزات الرئيسية

### 🔐 نظام المصادقة والتفويض
- تسجيل دخول آمن باستخدام JWT
- ثلاثة أدوار: مدير (ADMIN)، طبيب (DOCTOR)، موظف استقبال (RECEPTIONIST)
- تشفير كلمات المرور باستخدام BCrypt

### 👥 إدارة المرضى
- إضافة وتعديل وحذف بيانات المرضى
- البحث المتقدم بالاسم أو رقم الهاتف أو رقم الهوية
- رفع وإدارة الملفات الطبية (صور، أشعة)

### 📅 إدارة المواعيد
- حجز المواعيد مع اختيار الطبيب والتاريخ والوقت
- التحقق من توفر المواعيد
- إدارة جداول عمل الأطباء
- إرسال تنبيهات بالبريد الإلكتروني

### 📋 السجل الطبي
- عرض السجل الطبي الكامل لكل مريض
- إضافة التشخيصات والعلاجات
- رفع التقارير والملفات الطبية

### 💰 إدارة الفواتير والمدفوعات
- إنشاء فواتير تلقائية
- طرق دفع متعددة (نقدي، بطاقة، تأمين)
- توليد وطباعة الفواتير بصيغة PDF
- تقارير مالية يومية وشهرية

### 📊 لوحة التحكم والتقارير
- إحصائيات المرضى اليومية
- المواعيد المجدولة
- التقارير المالية والطبية

## التقنيات المستخدمة

### Backend
- **Java 17+**
- **Spring Boot 3.2.0**
  - Spring Web
  - Spring Data JPA
  - Spring Security
  - Spring Validation
  - Spring Mail
- **MySQL 8.0**
- **JWT** للمصادقة
- **Lombok** لتقليل الكود
- **Swagger/OpenAPI** للتوثيق

### Frontend
- **HTML5/CSS3/JavaScript**
- **Bootstrap 5** للتصميم المتجاوب
- **Thymeleaf** كمحرك القوالب

### أدوات إضافية
- **iText7** لتوليد ملفات PDF
- **Maven** لإدارة التبعيات
- **Spring Boot DevTools** للتطوير

## متطلبات التشغيل

### البرامج المطلوبة
- Java 17 أو أحدث
- MySQL 8.0 أو أحدث
- Maven 3.6 أو أحدث

### إعداد قاعدة البيانات
1. تثبيت MySQL وإنشاء قاعدة بيانات:
```sql
CREATE DATABASE dental_clinic_db;
```

2. تحديث إعدادات قاعدة البيانات في `application.yml`:
```yaml
spring:
  datasource:
    username: your_username
    password: your_password
```

## تشغيل المشروع

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd dental-clinic-management
```

### 2. تثبيت التبعيات
```bash
mvn clean install
```

### 3. تشغيل التطبيق
```bash
mvn spring-boot:run
```

### 4. الوصول للتطبيق
- التطبيق: http://localhost:8080/api
- Swagger UI: http://localhost:8080/api/swagger-ui.html
- API Docs: http://localhost:8080/api/api-docs

## هيكل المشروع

```
src/
├── main/
│   ├── java/com/dentalclinic/management/
│   │   ├── config/          # إعدادات التطبيق
│   │   ├── controller/      # REST Controllers
│   │   ├── dto/            # Data Transfer Objects
│   │   ├── entity/         # JPA Entities
│   │   ├── repository/     # Data Repositories
│   │   ├── security/       # إعدادات الأمان
│   │   ├── service/        # Business Logic
│   │   └── util/           # Utility Classes
│   └── resources/
│       ├── static/         # CSS, JS, Images
│       ├── templates/      # Thymeleaf Templates
│       └── application.yml # إعدادات التطبيق
└── test/                   # Unit Tests
```

## API Endpoints

### المصادقة
- `POST /auth/login` - تسجيل الدخول
- `POST /auth/register` - تسجيل مستخدم جديد
- `POST /auth/refresh` - تجديد الرمز المميز

### المرضى
- `GET /patients` - عرض جميع المرضى
- `POST /patients` - إضافة مريض جديد
- `GET /patients/{id}` - عرض مريض محدد
- `PUT /patients/{id}` - تحديث بيانات مريض
- `DELETE /patients/{id}` - حذف مريض

### المواعيد
- `GET /appointments` - عرض جميع المواعيد
- `POST /appointments` - حجز موعد جديد
- `PUT /appointments/{id}` - تحديث موعد
- `DELETE /appointments/{id}` - إلغاء موعد

## المساهمة
نرحب بالمساهمات! يرجى اتباع الخطوات التالية:
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push للفرع
5. إنشاء Pull Request

## الترخيص
هذا المشروع مرخص تحت رخصة MIT.

## الدعم
للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.
