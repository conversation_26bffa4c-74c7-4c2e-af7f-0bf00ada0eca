package com.dentalclinic.management.service;

import com.dentalclinic.management.entity.FileCategory;
import com.dentalclinic.management.entity.MedicalFile;
import com.dentalclinic.management.entity.Patient;
import com.dentalclinic.management.repository.MedicalFileRepository;
import com.dentalclinic.management.repository.PatientRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class MedicalFileService {
    
    private final MedicalFileRepository medicalFileRepository;
    private final PatientRepository patientRepository;
    private final FileStorageService fileStorageService;
    
    // Allowed file types for medical files
    private static final String[] ALLOWED_IMAGE_TYPES = {"image/jpeg", "image/png", "image/gif"};
    private static final String[] ALLOWED_DOCUMENT_TYPES = {"application/pdf", "application/msword", 
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"};
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    
    public MedicalFile uploadPatientFile(Long patientId, MultipartFile file, 
                                       FileCategory category, String description) {
        log.info("Uploading file for patient ID: {}", patientId);
        
        Patient patient = patientRepository.findByIdAndIsActiveTrue(patientId)
                .orElseThrow(() -> new RuntimeException("Patient not found with ID: " + patientId));
        
        // Validate file
        validateFile(file, category);
        
        // Store file
        String subDirectory = "patients/" + patientId + "/" + category.name().toLowerCase();
        String filePath = fileStorageService.storeFile(file, subDirectory);
        
        // Create medical file record
        MedicalFile medicalFile = new MedicalFile();
        medicalFile.setPatient(patient);
        medicalFile.setFileName(file.getOriginalFilename());
        medicalFile.setFilePath(filePath);
        medicalFile.setFileType(file.getContentType());
        medicalFile.setFileSize(file.getSize());
        medicalFile.setFileCategory(category);
        medicalFile.setDescription(description);
        
        MedicalFile savedFile = medicalFileRepository.save(medicalFile);
        log.info("Medical file uploaded successfully with ID: {}", savedFile.getId());
        
        return savedFile;
    }
    
    @Transactional(readOnly = true)
    public List<MedicalFile> getPatientFiles(Long patientId) {
        log.debug("Fetching files for patient ID: {}", patientId);
        return medicalFileRepository.findByPatientId(patientId);
    }
    
    @Transactional(readOnly = true)
    public List<MedicalFile> getPatientFilesByCategory(Long patientId, FileCategory category) {
        log.debug("Fetching {} files for patient ID: {}", category, patientId);
        return medicalFileRepository.findByPatientIdAndFileCategory(patientId, category);
    }
    
    public void deleteFile(Long fileId) {
        log.info("Deleting medical file with ID: {}", fileId);
        
        MedicalFile medicalFile = medicalFileRepository.findById(fileId)
                .orElseThrow(() -> new RuntimeException("Medical file not found with ID: " + fileId));
        
        // Delete physical file
        fileStorageService.deleteFile(medicalFile.getFilePath());
        
        // Delete database record
        medicalFileRepository.delete(medicalFile);
        
        log.info("Medical file deleted successfully with ID: {}", fileId);
    }
    
    @Transactional(readOnly = true)
    public MedicalFile getFileById(Long fileId) {
        return medicalFileRepository.findById(fileId)
                .orElseThrow(() -> new RuntimeException("Medical file not found with ID: " + fileId));
    }
    
    private void validateFile(MultipartFile file, FileCategory category) {
        if (file.isEmpty()) {
            throw new RuntimeException("File is empty");
        }
        
        if (!fileStorageService.isValidFileSize(file, MAX_FILE_SIZE)) {
            throw new RuntimeException("File size exceeds maximum allowed size of 10MB");
        }
        
        boolean isValidType = false;
        switch (category) {
            case X_RAY:
            case PHOTO:
                isValidType = fileStorageService.isValidFileType(file, ALLOWED_IMAGE_TYPES);
                break;
            case REPORT:
            case PRESCRIPTION:
            case INSURANCE_DOCUMENT:
                isValidType = fileStorageService.isValidFileType(file, ALLOWED_DOCUMENT_TYPES) ||
                             fileStorageService.isValidFileType(file, ALLOWED_IMAGE_TYPES);
                break;
            case OTHER:
                isValidType = fileStorageService.isValidFileType(file, ALLOWED_DOCUMENT_TYPES) ||
                             fileStorageService.isValidFileType(file, ALLOWED_IMAGE_TYPES);
                break;
        }
        
        if (!isValidType) {
            throw new RuntimeException("Invalid file type for category: " + category);
        }
    }
}
