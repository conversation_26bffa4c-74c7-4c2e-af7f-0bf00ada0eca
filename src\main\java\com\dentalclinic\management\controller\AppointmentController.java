package com.dentalclinic.management.controller;

import com.dentalclinic.management.dto.AppointmentCreateRequest;
import com.dentalclinic.management.dto.AppointmentDto;
import com.dentalclinic.management.dto.MessageResponse;
import com.dentalclinic.management.entity.AppointmentStatus;
import com.dentalclinic.management.service.AppointmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/appointments")
@RequiredArgsConstructor
@Tag(name = "Appointment Management", description = "APIs for managing appointments")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AppointmentController {
    
    private final AppointmentService appointmentService;
    
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Create a new appointment", description = "Creates a new appointment")
    public ResponseEntity<?> createAppointment(@Valid @RequestBody AppointmentCreateRequest request) {
        try {
            AppointmentDto createdAppointment = appointmentService.createAppointment(request);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdAppointment);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get appointment by ID", description = "Retrieves an appointment by its ID")
    public ResponseEntity<?> getAppointmentById(
            @Parameter(description = "Appointment ID") @PathVariable Long id) {
        return appointmentService.getAppointmentById(id)
                .map(appointment -> ResponseEntity.ok().body(appointment))
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get all appointments", description = "Retrieves all appointments with pagination")
    public ResponseEntity<Page<AppointmentDto>> getAllAppointments(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "appointmentDateTime") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "asc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<AppointmentDto> appointments = appointmentService.getAllAppointments(pageable);
        return ResponseEntity.ok(appointments);
    }
    
    @GetMapping("/patient/{patientId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get appointments by patient", description = "Retrieves all appointments for a specific patient")
    public ResponseEntity<List<AppointmentDto>> getAppointmentsByPatient(
            @Parameter(description = "Patient ID") @PathVariable Long patientId) {
        
        List<AppointmentDto> appointments = appointmentService.getAppointmentsByPatient(patientId);
        return ResponseEntity.ok(appointments);
    }
    
    @GetMapping("/doctor/{doctorId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get appointments by doctor", description = "Retrieves all appointments for a specific doctor")
    public ResponseEntity<List<AppointmentDto>> getAppointmentsByDoctor(
            @Parameter(description = "Doctor ID") @PathVariable Long doctorId) {
        
        List<AppointmentDto> appointments = appointmentService.getAppointmentsByDoctor(doctorId);
        return ResponseEntity.ok(appointments);
    }
    
    @GetMapping("/today")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get today's appointments", description = "Retrieves all appointments for today")
    public ResponseEntity<List<AppointmentDto>> getTodayAppointments() {
        List<AppointmentDto> appointments = appointmentService.getTodayAppointments();
        return ResponseEntity.ok(appointments);
    }
    
    @GetMapping("/today/doctor/{doctorId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get today's appointments by doctor", description = "Retrieves today's appointments for a specific doctor")
    public ResponseEntity<List<AppointmentDto>> getTodayAppointmentsByDoctor(
            @Parameter(description = "Doctor ID") @PathVariable Long doctorId) {

        List<AppointmentDto> appointments = appointmentService.getTodayAppointmentsByDoctor(doctorId);
        return ResponseEntity.ok(appointments);
    }

    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Update appointment status", description = "Updates the status of an appointment")
    public ResponseEntity<?> updateAppointmentStatus(
            @Parameter(description = "Appointment ID") @PathVariable Long id,
            @Parameter(description = "New status") @RequestParam AppointmentStatus status,
            @Parameter(description = "Reason for cancellation") @RequestParam(required = false) String reason) {

        try {
            AppointmentDto updatedAppointment = appointmentService.updateAppointmentStatus(id, status, reason);
            return ResponseEntity.ok(updatedAppointment);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }

    @PutMapping("/{id}/reschedule")
    @PreAuthorize("hasRole('ADMIN') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Reschedule appointment", description = "Reschedules an appointment to a new date and time")
    public ResponseEntity<?> rescheduleAppointment(
            @Parameter(description = "Appointment ID") @PathVariable Long id,
            @Parameter(description = "New date and time") @RequestParam
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime newDateTime) {

        try {
            AppointmentDto rescheduledAppointment = appointmentService.rescheduleAppointment(id, newDateTime);
            return ResponseEntity.ok(rescheduledAppointment);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }

    @GetMapping("/availability")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Check time slot availability", description = "Checks if a time slot is available for a doctor")
    public ResponseEntity<Boolean> checkTimeSlotAvailability(
            @Parameter(description = "Doctor ID") @RequestParam Long doctorId,
            @Parameter(description = "Date and time") @RequestParam
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime dateTime,
            @Parameter(description = "Duration in minutes") @RequestParam(defaultValue = "30") Integer durationMinutes) {

        boolean isAvailable = appointmentService.isTimeSlotAvailable(doctorId, dateTime, durationMinutes);
        return ResponseEntity.ok(isAvailable);
    }

    @GetMapping("/stats/today/total")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get today's appointments count", description = "Returns the total number of appointments for today")
    public ResponseEntity<Long> getTodayAppointmentsCount() {
        Long count = appointmentService.getTodayAppointmentsCount();
        return ResponseEntity.ok(count);
    }

    @GetMapping("/stats/today/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get today's appointments count by status", description = "Returns the number of appointments for today by status")
    public ResponseEntity<Long> getTodayAppointmentsCountByStatus(
            @Parameter(description = "Appointment status") @RequestParam AppointmentStatus status) {

        Long count = appointmentService.getTodayAppointmentsCountByStatus(status);
        return ResponseEntity.ok(count);
    }
}
