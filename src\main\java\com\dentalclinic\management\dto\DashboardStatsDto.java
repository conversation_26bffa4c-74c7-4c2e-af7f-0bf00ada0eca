package com.dentalclinic.management.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class DashboardStatsDto {
    
    // Patient Statistics
    private Long totalPatients;
    private Long todayRegistrations;
    
    // Doctor Statistics
    private Long totalDoctors;
    
    // Appointment Statistics
    private Long todayAppointments;
    private Long scheduledAppointments;
    private Long completedAppointments;
    private Long cancelledAppointments;
    
    // Medical Record Statistics
    private Long todayMedicalRecords;
    
    // Financial Statistics
    private Long todayInvoices;
    private BigDecimal todayRevenue;
    private BigDecimal todayPaidAmount;
    private BigDecimal todayPendingAmount;
    private Long pendingInvoices;
    private Long overdueInvoices;
    
    // Payment Statistics
    private Long todayPayments;
    private BigDecimal todayPaymentsTotal;
}
