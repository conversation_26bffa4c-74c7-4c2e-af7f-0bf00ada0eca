package com.dentalclinic.management.service;

import com.dentalclinic.management.dto.PaymentCreateRequest;
import com.dentalclinic.management.dto.PaymentDto;
import com.dentalclinic.management.entity.Invoice;
import com.dentalclinic.management.entity.InvoiceStatus;
import com.dentalclinic.management.entity.Payment;
import com.dentalclinic.management.entity.PaymentMethod;
import com.dentalclinic.management.repository.InvoiceRepository;
import com.dentalclinic.management.repository.PaymentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class PaymentService {
    
    private final PaymentRepository paymentRepository;
    private final InvoiceRepository invoiceRepository;
    
    public PaymentDto createPayment(PaymentCreateRequest request) {
        log.info("Creating payment for invoice ID: {}", request.getInvoiceId());
        
        // Validate invoice exists
        Invoice invoice = invoiceRepository.findById(request.getInvoiceId())
                .orElseThrow(() -> new RuntimeException("Invoice not found with ID: " + request.getInvoiceId()));
        
        // Validate payment amount
        BigDecimal remainingAmount = invoice.getRemainingAmount();
        if (request.getPaymentAmount().compareTo(remainingAmount) > 0) {
            throw new RuntimeException("Payment amount cannot exceed remaining amount: " + remainingAmount);
        }
        
        if (request.getPaymentAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("Payment amount must be positive");
        }
        
        // Create payment
        Payment payment = mapToEntity(request, invoice);
        Payment savedPayment = paymentRepository.save(payment);
        
        // Update invoice paid amount and status
        updateInvoiceAfterPayment(invoice, request.getPaymentAmount());
        
        log.info("Payment created successfully with ID: {}", savedPayment.getId());
        return mapToDto(savedPayment);
    }
    
    @Transactional(readOnly = true)
    public List<PaymentDto> getPaymentsByInvoice(Long invoiceId) {
        log.debug("Fetching payments for invoice ID: {}", invoiceId);
        return paymentRepository.findByInvoiceId(invoiceId)
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public List<PaymentDto> getPaymentsByMethod(PaymentMethod paymentMethod) {
        log.debug("Fetching payments by method: {}", paymentMethod);
        return paymentRepository.findByPaymentMethod(paymentMethod)
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public List<PaymentDto> getPaymentsByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("Fetching payments between {} and {}", startDate, endDate);
        return paymentRepository.findByPaymentDateBetween(startDate, endDate)
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public List<PaymentDto> getTodayPayments() {
        log.debug("Fetching today's payments");
        return paymentRepository.findTodayPayments()
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public Long getTodayPaymentsCount() {
        return paymentRepository.countTodayPayments();
    }
    
    @Transactional(readOnly = true)
    public BigDecimal getTodayPaymentsTotal() {
        BigDecimal total = paymentRepository.getTodayPaymentsTotal();
        return total != null ? total : BigDecimal.ZERO;
    }
    
    @Transactional(readOnly = true)
    public BigDecimal getPaymentsTotalByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        BigDecimal total = paymentRepository.getPaymentsTotalByDateRange(startDate, endDate);
        return total != null ? total : BigDecimal.ZERO;
    }
    
    private void updateInvoiceAfterPayment(Invoice invoice, BigDecimal paymentAmount) {
        BigDecimal newPaidAmount = invoice.getPaidAmount().add(paymentAmount);
        invoice.setPaidAmount(newPaidAmount);
        
        // Update invoice status based on payment
        if (invoice.isFullyPaid()) {
            invoice.setStatus(InvoiceStatus.PAID);
        } else if (newPaidAmount.compareTo(BigDecimal.ZERO) > 0) {
            invoice.setStatus(InvoiceStatus.PARTIALLY_PAID);
        }
        
        invoiceRepository.save(invoice);
        log.info("Invoice ID: {} updated with new paid amount: {}", invoice.getId(), newPaidAmount);
    }
    
    private Payment mapToEntity(PaymentCreateRequest request, Invoice invoice) {
        Payment payment = new Payment();
        payment.setInvoice(invoice);
        payment.setPaymentAmount(request.getPaymentAmount());
        payment.setPaymentMethod(request.getPaymentMethod());
        payment.setPaymentDate(request.getPaymentDate());
        payment.setTransactionReference(request.getTransactionReference());
        payment.setNotes(request.getNotes());
        return payment;
    }
    
    private PaymentDto mapToDto(Payment payment) {
        PaymentDto dto = new PaymentDto();
        dto.setId(payment.getId());
        dto.setInvoiceId(payment.getInvoice().getId());
        dto.setInvoiceNumber(payment.getInvoice().getInvoiceNumber());
        dto.setPaymentAmount(payment.getPaymentAmount());
        dto.setPaymentMethod(payment.getPaymentMethod());
        dto.setPaymentDate(payment.getPaymentDate());
        dto.setTransactionReference(payment.getTransactionReference());
        dto.setNotes(payment.getNotes());
        dto.setCreatedAt(payment.getCreatedAt());
        return dto;
    }
}
