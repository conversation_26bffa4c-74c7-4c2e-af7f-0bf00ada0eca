package com.dentalclinic.management.controller;

import com.dentalclinic.management.dto.MessageResponse;
import com.dentalclinic.management.entity.FileCategory;
import com.dentalclinic.management.entity.MedicalFile;
import com.dentalclinic.management.service.FileStorageService;
import com.dentalclinic.management.service.MedicalFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;
import java.util.List;

@RestController
@RequestMapping("/files")
@RequiredArgsConstructor
@Tag(name = "File Management", description = "APIs for managing medical files")
@CrossOrigin(origins = "*", maxAge = 3600)
public class FileController {
    
    private final MedicalFileService medicalFileService;
    private final FileStorageService fileStorageService;
    
    @PostMapping("/patients/{patientId}/upload")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Upload patient file", description = "Upload a medical file for a patient")
    public ResponseEntity<?> uploadPatientFile(
            @Parameter(description = "Patient ID") @PathVariable Long patientId,
            @Parameter(description = "File to upload") @RequestParam("file") MultipartFile file,
            @Parameter(description = "File category") @RequestParam("category") FileCategory category,
            @Parameter(description = "File description") @RequestParam(value = "description", required = false) String description) {
        
        try {
            MedicalFile medicalFile = medicalFileService.uploadPatientFile(patientId, file, category, description);
            return ResponseEntity.ok(medicalFile);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }
    
    @GetMapping("/patients/{patientId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get patient files", description = "Get all files for a specific patient")
    public ResponseEntity<List<MedicalFile>> getPatientFiles(
            @Parameter(description = "Patient ID") @PathVariable Long patientId) {
        
        List<MedicalFile> files = medicalFileService.getPatientFiles(patientId);
        return ResponseEntity.ok(files);
    }
    
    @GetMapping("/patients/{patientId}/category/{category}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Get patient files by category", description = "Get files for a patient filtered by category")
    public ResponseEntity<List<MedicalFile>> getPatientFilesByCategory(
            @Parameter(description = "Patient ID") @PathVariable Long patientId,
            @Parameter(description = "File category") @PathVariable FileCategory category) {
        
        List<MedicalFile> files = medicalFileService.getPatientFilesByCategory(patientId, category);
        return ResponseEntity.ok(files);
    }
    
    @GetMapping("/download/{fileId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR') or hasRole('RECEPTIONIST')")
    @Operation(summary = "Download file", description = "Download a medical file by ID")
    public ResponseEntity<Resource> downloadFile(
            @Parameter(description = "File ID") @PathVariable Long fileId) {
        
        try {
            MedicalFile medicalFile = medicalFileService.getFileById(fileId);
            Path filePath = fileStorageService.loadFileAsResource(medicalFile.getFilePath());
            Resource resource = new UrlResource(filePath.toUri());
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(medicalFile.getFileType()))
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                           "attachment; filename=\"" + medicalFile.getFileName() + "\"")
                    .body(resource);
                    
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }
    
    @DeleteMapping("/{fileId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('DOCTOR')")
    @Operation(summary = "Delete file", description = "Delete a medical file")
    public ResponseEntity<?> deleteFile(
            @Parameter(description = "File ID") @PathVariable Long fileId) {
        
        try {
            medicalFileService.deleteFile(fileId);
            return ResponseEntity.ok(new MessageResponse("File deleted successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }
}
