package com.dentalclinic.management.service;

import com.dentalclinic.management.dto.DashboardStatsDto;
import com.dentalclinic.management.entity.AppointmentStatus;
import com.dentalclinic.management.entity.InvoiceStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class DashboardService {
    
    private final PatientService patientService;
    private final DoctorService doctorService;
    private final AppointmentService appointmentService;
    private final MedicalRecordService medicalRecordService;
    private final InvoiceService invoiceService;
    private final PaymentService paymentService;
    
    public DashboardStatsDto getDashboardStats() {
        log.info("Generating dashboard statistics");
        
        DashboardStatsDto stats = new DashboardStatsDto();
        
        // Patient Statistics
        stats.setTotalPatients(patientService.getTotalPatientsCount());
        stats.setTodayRegistrations(patientService.getTodayRegistrationsCount());
        
        // Doctor Statistics
        stats.setTotalDoctors(doctorService.getTotalDoctorsCount());
        
        // Appointment Statistics
        stats.setTodayAppointments(appointmentService.getTodayAppointmentsCount());
        stats.setScheduledAppointments(appointmentService.getTodayAppointmentsCountByStatus(AppointmentStatus.SCHEDULED));
        stats.setCompletedAppointments(appointmentService.getTodayAppointmentsCountByStatus(AppointmentStatus.COMPLETED));
        stats.setCancelledAppointments(appointmentService.getTodayAppointmentsCountByStatus(AppointmentStatus.CANCELLED));
        
        // Medical Record Statistics
        stats.setTodayMedicalRecords(medicalRecordService.getTodayMedicalRecordsCount());
        
        // Financial Statistics
        stats.setTodayInvoices(invoiceService.getTodayInvoicesCount());
        stats.setTodayRevenue(invoiceService.getTodayTotalRevenue());
        stats.setTodayPaidAmount(invoiceService.getTodayPaidAmount());
        
        BigDecimal todayPendingAmount = stats.getTodayRevenue().subtract(stats.getTodayPaidAmount());
        stats.setTodayPendingAmount(todayPendingAmount);
        
        stats.setPendingInvoices(invoiceService.getInvoicesCountByStatus(InvoiceStatus.PENDING));
        stats.setOverdueInvoices(invoiceService.getInvoicesCountByStatus(InvoiceStatus.OVERDUE));
        
        // Payment Statistics
        stats.setTodayPayments(paymentService.getTodayPaymentsCount());
        stats.setTodayPaymentsTotal(paymentService.getTodayPaymentsTotal());
        
        log.info("Dashboard statistics generated successfully");
        return stats;
    }
}
