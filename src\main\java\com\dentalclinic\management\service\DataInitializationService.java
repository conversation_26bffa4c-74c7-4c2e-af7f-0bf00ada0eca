package com.dentalclinic.management.service;

import com.dentalclinic.management.entity.Role;
import com.dentalclinic.management.entity.RoleName;
import com.dentalclinic.management.entity.User;
import com.dentalclinic.management.repository.RoleRepository;
import com.dentalclinic.management.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;

@Service
@RequiredArgsConstructor
@Slf4j
public class DataInitializationService implements CommandLineRunner {
    
    private final RoleRepository roleRepository;
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    
    @Override
    @Transactional
    public void run(String... args) {
        log.info("Starting data initialization...");
        
        initializeRoles();
        initializeDefaultUsers();
        
        log.info("Data initialization completed successfully");
    }
    
    private void initializeRoles() {
        log.info("Initializing roles...");
        
        for (RoleName roleName : RoleName.values()) {
            if (!roleRepository.existsByName(roleName)) {
                Role role = new Role(roleName);
                roleRepository.save(role);
                log.info("Created role: {}", roleName);
            }
        }
    }
    
    private void initializeDefaultUsers() {
        log.info("Initializing default users...");
        
        // Create admin user
        if (!userRepository.existsByUsername("admin")) {
            User admin = new User();
            admin.setUsername("admin");
            admin.setEmail("<EMAIL>");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setFirstName("مدير");
            admin.setLastName("النظام");
            admin.setPhoneNumber("0501234567");
            admin.setIsActive(true);
            
            Role adminRole = roleRepository.findByName(RoleName.ROLE_ADMIN)
                    .orElseThrow(() -> new RuntimeException("Admin role not found"));
            admin.setRoles(Set.of(adminRole));
            
            userRepository.save(admin);
            log.info("Created admin user: admin");
        }
        
        // Create doctor user
        if (!userRepository.existsByUsername("doctor")) {
            User doctor = new User();
            doctor.setUsername("doctor");
            doctor.setEmail("<EMAIL>");
            doctor.setPassword(passwordEncoder.encode("doctor123"));
            doctor.setFirstName("د. أحمد");
            doctor.setLastName("محمد");
            doctor.setPhoneNumber("0507654321");
            doctor.setIsActive(true);
            
            Role doctorRole = roleRepository.findByName(RoleName.ROLE_DOCTOR)
                    .orElseThrow(() -> new RuntimeException("Doctor role not found"));
            doctor.setRoles(Set.of(doctorRole));
            
            userRepository.save(doctor);
            log.info("Created doctor user: doctor");
        }
        
        // Create receptionist user
        if (!userRepository.existsByUsername("receptionist")) {
            User receptionist = new User();
            receptionist.setUsername("receptionist");
            receptionist.setEmail("<EMAIL>");
            receptionist.setPassword(passwordEncoder.encode("receptionist123"));
            receptionist.setFirstName("فاطمة");
            receptionist.setLastName("علي");
            receptionist.setPhoneNumber("0509876543");
            receptionist.setIsActive(true);
            
            Role receptionistRole = roleRepository.findByName(RoleName.ROLE_RECEPTIONIST)
                    .orElseThrow(() -> new RuntimeException("Receptionist role not found"));
            receptionist.setRoles(Set.of(receptionistRole));
            
            userRepository.save(receptionist);
            log.info("Created receptionist user: receptionist");
        }
    }
}
