@echo off
echo ========================================
echo   نظام إدارة عيادة الأسنان
echo   Dental Clinic Management System
echo ========================================
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Java متوفر بالفعل
    goto :run_app
)

echo ❌ Java غير مثبت
echo.
echo 🔄 جاري تحميل وتثبيت Java 17 تلقائياً...
echo.

REM Create temp directory
if not exist "temp" mkdir temp

REM Download Java 17 using PowerShell
echo 📥 تحميل Java 17...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://github.com/adoptium/temurin17-binaries/releases/download/jdk-********+1/OpenJDK17U-jdk_x64_windows_hotspot_********_1.msi' -OutFile 'temp\java17.msi'}"

if %errorlevel% neq 0 (
    echo ❌ فشل في تحميل Java
    echo يرجى تحميل Java يدوياً من: https://adoptium.net/
    pause
    exit /b 1
)

echo ✅ تم تحميل Java بنجاح
echo.
echo 🔧 جاري تثبيت Java...
echo (قد يطلب منك إذن المدير)

REM Install Java silently
msiexec /i "temp\java17.msi" /quiet /norestart ADDLOCAL=FeatureMain,FeatureEnvironment,FeatureJarFileRunWith,FeatureJavaHome

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت Java تلقائياً
    echo يرجى تشغيل الملف temp\java17.msi يدوياً
    echo ثم إعادة تشغيل هذا الملف
    pause
    exit /b 1
)

echo ✅ تم تثبيت Java بنجاح
echo.
echo 🔄 إعادة تحديث متغيرات البيئة...

REM Refresh environment variables
call refreshenv.cmd >nul 2>&1

REM Add Java to PATH for current session
for /f "tokens=2*" %%i in ('reg query "HKLM\SOFTWARE\Eclipse Adoptium\JDK\********\hotspot\MSI" /v Path 2^>nul') do set JAVA_HOME=%%j
if defined JAVA_HOME (
    set PATH=%JAVA_HOME%\bin;%PATH%
    echo ✅ تم إعداد Java في PATH
) else (
    echo ⚠️ قد تحتاج لإعادة تشغيل Command Prompt
)

echo.
echo 🧹 تنظيف الملفات المؤقتة...
del /q "temp\java17.msi" >nul 2>&1
rmdir "temp" >nul 2>&1

:run_app
echo.
echo 🚀 بدء تشغيل التطبيق...
echo.

REM Check Java again
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java لا يزال غير متاح
    echo يرجى إعادة تشغيل Command Prompt وتشغيل الملف مرة أخرى
    pause
    exit /b 1
)

echo ✅ Java جاهز
java -version
echo.

echo 🌐 سيكون التطبيق متاحاً على:
echo    - الصفحة الرئيسية: http://localhost:8080/
echo    - تسجيل الدخول: http://localhost:8080/login
echo    - Swagger API: http://localhost:8080/api/swagger-ui.html
echo    - قاعدة البيانات H2: http://localhost:8080/api/h2-console
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    - مدير: admin / admin123
echo    - طبيب: doctor / doctor123
echo    - استقبال: receptionist / receptionist123
echo.
echo ⏳ جاري تحميل التبعيات وتشغيل التطبيق...
echo    (قد يستغرق بضع دقائق في المرة الأولى)
echo.
echo 🛑 لإيقاف التطبيق اضغط Ctrl+C
echo.

REM Run the application
if exist "mvnw.cmd" (
    .\mvnw.cmd spring-boot:run
) else (
    echo ❌ Maven Wrapper غير موجود
    echo يرجى التأكد من وجود ملف mvnw.cmd
    pause
)

pause
