package com.dentalclinic.management.service;

import com.dentalclinic.management.dto.PatientCreateRequest;
import com.dentalclinic.management.dto.PatientDto;
import com.dentalclinic.management.entity.Gender;
import com.dentalclinic.management.entity.Patient;
import com.dentalclinic.management.repository.PatientRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PatientServiceTest {

    @Mock
    private PatientRepository patientRepository;

    @InjectMocks
    private PatientService patientService;

    private Patient testPatient;
    private PatientCreateRequest createRequest;

    @BeforeEach
    void setUp() {
        testPatient = new Patient();
        testPatient.setId(1L);
        testPatient.setFirstName("أحمد");
        testPatient.setLastName("محمد");
        testPatient.setDateOfBirth(LocalDate.of(1990, 1, 1));
        testPatient.setGender(Gender.MALE);
        testPatient.setPhoneNumber("**********");
        testPatient.setEmail("<EMAIL>");
        testPatient.setNationalId("**********");
        testPatient.setIsActive(true);
        testPatient.setCreatedAt(LocalDateTime.now());
        testPatient.setUpdatedAt(LocalDateTime.now());

        createRequest = new PatientCreateRequest();
        createRequest.setFirstName("أحمد");
        createRequest.setLastName("محمد");
        createRequest.setDateOfBirth(LocalDate.of(1990, 1, 1));
        createRequest.setGender(Gender.MALE);
        createRequest.setPhoneNumber("**********");
        createRequest.setEmail("<EMAIL>");
        createRequest.setNationalId("**********");
    }

    @Test
    void testCreatePatient_Success() {
        // Arrange
        when(patientRepository.existsByNationalId(anyString())).thenReturn(false);
        when(patientRepository.existsByEmail(anyString())).thenReturn(false);
        when(patientRepository.save(any(Patient.class))).thenReturn(testPatient);

        // Act
        PatientDto result = patientService.createPatient(createRequest);

        // Assert
        assertNotNull(result);
        assertEquals("أحمد", result.getFirstName());
        assertEquals("محمد", result.getLastName());
        assertEquals("<EMAIL>", result.getEmail());
        verify(patientRepository).save(any(Patient.class));
    }

    @Test
    void testCreatePatient_DuplicateNationalId() {
        // Arrange
        when(patientRepository.existsByNationalId(anyString())).thenReturn(true);

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            patientService.createPatient(createRequest);
        });
        assertEquals("Patient with this National ID already exists", exception.getMessage());
        verify(patientRepository, never()).save(any(Patient.class));
    }

    @Test
    void testCreatePatient_DuplicateEmail() {
        // Arrange
        when(patientRepository.existsByNationalId(anyString())).thenReturn(false);
        when(patientRepository.existsByEmail(anyString())).thenReturn(true);

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            patientService.createPatient(createRequest);
        });
        assertEquals("Patient with this email already exists", exception.getMessage());
        verify(patientRepository, never()).save(any(Patient.class));
    }

    @Test
    void testGetPatientById_Found() {
        // Arrange
        when(patientRepository.findByIdAndIsActiveTrue(anyLong())).thenReturn(Optional.of(testPatient));

        // Act
        Optional<PatientDto> result = patientService.getPatientById(1L);

        // Assert
        assertTrue(result.isPresent());
        assertEquals("أحمد", result.get().getFirstName());
        assertEquals("محمد", result.get().getLastName());
    }

    @Test
    void testGetPatientById_NotFound() {
        // Arrange
        when(patientRepository.findByIdAndIsActiveTrue(anyLong())).thenReturn(Optional.empty());

        // Act
        Optional<PatientDto> result = patientService.getPatientById(1L);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    void testGetAllPatients() {
        // Arrange
        List<Patient> patients = Arrays.asList(testPatient);
        Page<Patient> patientPage = new PageImpl<>(patients);
        Pageable pageable = PageRequest.of(0, 10);
        
        when(patientRepository.findByIsActiveTrue(pageable)).thenReturn(patientPage);

        // Act
        Page<PatientDto> result = patientService.getAllPatients(pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("أحمد", result.getContent().get(0).getFirstName());
    }

    @Test
    void testUpdatePatient_Success() {
        // Arrange
        when(patientRepository.findByIdAndIsActiveTrue(anyLong())).thenReturn(Optional.of(testPatient));
        when(patientRepository.existsByNationalId(anyString())).thenReturn(false);
        when(patientRepository.existsByEmail(anyString())).thenReturn(false);
        when(patientRepository.save(any(Patient.class))).thenReturn(testPatient);

        createRequest.setFirstName("محمد");
        createRequest.setLastName("أحمد");

        // Act
        PatientDto result = patientService.updatePatient(1L, createRequest);

        // Assert
        assertNotNull(result);
        verify(patientRepository).save(any(Patient.class));
    }

    @Test
    void testUpdatePatient_NotFound() {
        // Arrange
        when(patientRepository.findByIdAndIsActiveTrue(anyLong())).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            patientService.updatePatient(1L, createRequest);
        });
        assertEquals("Patient not found with ID: 1", exception.getMessage());
    }

    @Test
    void testDeletePatient_Success() {
        // Arrange
        when(patientRepository.findByIdAndIsActiveTrue(anyLong())).thenReturn(Optional.of(testPatient));
        when(patientRepository.save(any(Patient.class))).thenReturn(testPatient);

        // Act
        patientService.deletePatient(1L);

        // Assert
        verify(patientRepository).save(any(Patient.class));
    }

    @Test
    void testDeletePatient_NotFound() {
        // Arrange
        when(patientRepository.findByIdAndIsActiveTrue(anyLong())).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            patientService.deletePatient(1L);
        });
        assertEquals("Patient not found with ID: 1", exception.getMessage());
    }

    @Test
    void testSearchPatients() {
        // Arrange
        List<Patient> patients = Arrays.asList(testPatient);
        Page<Patient> patientPage = new PageImpl<>(patients);
        Pageable pageable = PageRequest.of(0, 10);
        
        when(patientRepository.searchPatients(anyString(), any(Pageable.class))).thenReturn(patientPage);

        // Act
        Page<PatientDto> result = patientService.searchPatients("أحمد", pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("أحمد", result.getContent().get(0).getFirstName());
    }
}
