package com.dentalclinic.management.repository;

import com.dentalclinic.management.entity.Payment;
import com.dentalclinic.management.entity.PaymentMethod;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PaymentRepository extends JpaRepository<Payment, Long> {
    
    List<Payment> findByInvoiceId(Long invoiceId);
    
    List<Payment> findByPaymentMethod(PaymentMethod paymentMethod);
    
    @Query("SELECT p FROM Payment p WHERE p.paymentDate BETWEEN :startDate AND :endDate")
    List<Payment> findByPaymentDateBetween(@Param("startDate") LocalDateTime startDate,
                                          @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT p FROM Payment p WHERE DATE(p.paymentDate) = CURRENT_DATE")
    List<Payment> findTodayPayments();
    
    @Query("SELECT COUNT(p) FROM Payment p WHERE DATE(p.paymentDate) = CURRENT_DATE")
    Long countTodayPayments();
    
    @Query("SELECT SUM(p.paymentAmount) FROM Payment p WHERE DATE(p.paymentDate) = CURRENT_DATE")
    BigDecimal getTodayPaymentsTotal();
    
    @Query("SELECT SUM(p.paymentAmount) FROM Payment p WHERE p.paymentDate BETWEEN :startDate AND :endDate")
    BigDecimal getPaymentsTotalByDateRange(@Param("startDate") LocalDateTime startDate,
                                          @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT p.paymentMethod, SUM(p.paymentAmount) FROM Payment p " +
           "WHERE p.paymentDate BETWEEN :startDate AND :endDate " +
           "GROUP BY p.paymentMethod")
    List<Object[]> getPaymentsByMethodAndDateRange(@Param("startDate") LocalDateTime startDate,
                                                  @Param("endDate") LocalDateTime endDate);
}
