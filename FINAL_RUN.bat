@echo off
title Dental Clinic Management System

echo ========================================
echo   Dental Clinic Management System
echo   نظام إدارة عيادة الأسنان
echo ========================================
echo.

REM Set Java environment
set "JAVA_HOME=C:\Program Files\Java\jdk-24"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo Checking Java installation...
"%JAVA_HOME%\bin\java.exe" -version
if %errorlevel% neq 0 (
    echo Error: Java not found at %JAVA_HOME%
    echo Please install Java 17+ from https://adoptium.net/
    pause
    exit /b 1
)

echo.
echo Java is ready!
echo.
echo Starting Dental Clinic Management System...
echo.
echo Application will be available at:
echo   - Main page: http://localhost:8080/
echo   - Login: http://localhost:8080/login
echo   - API docs: http://localhost:8080/api/swagger-ui.html
echo   - H2 Database: http://localhost:8080/api/h2-console
echo.
echo Default credentials:
echo   - Admin: admin / admin123
echo   - Doctor: doctor / doctor123
echo   - Receptionist: receptionist / receptionist123
echo.
echo Starting application (may take 3-5 minutes first time)...
echo Press Ctrl+C to stop
echo.

REM Start the application
mvnw.cmd clean spring-boot:run

echo.
echo Application stopped
pause
