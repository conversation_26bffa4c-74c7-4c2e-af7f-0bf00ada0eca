package com.dentalclinic.management.service;

import com.dentalclinic.management.dto.PatientCreateRequest;
import com.dentalclinic.management.dto.PatientDto;
import com.dentalclinic.management.entity.Patient;
import com.dentalclinic.management.repository.PatientRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class PatientService {
    
    private final PatientRepository patientRepository;
    
    public PatientDto createPatient(PatientCreateRequest request) {
        log.info("Creating new patient: {} {}", request.getFirstName(), request.getLastName());
        
        // Check if patient with same national ID already exists
        if (request.getNationalId() != null && 
            patientRepository.existsByNationalId(request.getNationalId())) {
            throw new RuntimeException("Patient with this National ID already exists");
        }
        
        // Check if patient with same email already exists
        if (request.getEmail() != null && 
            patientRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("Patient with this email already exists");
        }
        
        Patient patient = mapToEntity(request);
        Patient savedPatient = patientRepository.save(patient);
        
        log.info("Patient created successfully with ID: {}", savedPatient.getId());
        return mapToDto(savedPatient);
    }
    
    @Transactional(readOnly = true)
    public Optional<PatientDto> getPatientById(Long id) {
        log.debug("Fetching patient with ID: {}", id);
        return patientRepository.findByIdAndIsActiveTrue(id)
                .map(this::mapToDto);
    }
    
    @Transactional(readOnly = true)
    public Page<PatientDto> getAllPatients(Pageable pageable) {
        log.debug("Fetching all active patients with pagination");
        return patientRepository.findByIsActiveTrue(pageable)
                .map(this::mapToDto);
    }
    
    @Transactional(readOnly = true)
    public Page<PatientDto> searchPatients(String searchTerm, Pageable pageable) {
        log.debug("Searching patients with term: {}", searchTerm);
        return patientRepository.searchPatients(searchTerm, pageable)
                .map(this::mapToDto);
    }
    
    public PatientDto updatePatient(Long id, PatientCreateRequest request) {
        log.info("Updating patient with ID: {}", id);
        
        Patient patient = patientRepository.findByIdAndIsActiveTrue(id)
                .orElseThrow(() -> new RuntimeException("Patient not found with ID: " + id));
        
        // Check if national ID is being changed and if it already exists
        if (request.getNationalId() != null && 
            !request.getNationalId().equals(patient.getNationalId()) &&
            patientRepository.existsByNationalId(request.getNationalId())) {
            throw new RuntimeException("Patient with this National ID already exists");
        }
        
        // Check if email is being changed and if it already exists
        if (request.getEmail() != null && 
            !request.getEmail().equals(patient.getEmail()) &&
            patientRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("Patient with this email already exists");
        }
        
        updatePatientFromRequest(patient, request);
        Patient updatedPatient = patientRepository.save(patient);
        
        log.info("Patient updated successfully with ID: {}", updatedPatient.getId());
        return mapToDto(updatedPatient);
    }
    
    public void deletePatient(Long id) {
        log.info("Soft deleting patient with ID: {}", id);
        
        Patient patient = patientRepository.findByIdAndIsActiveTrue(id)
                .orElseThrow(() -> new RuntimeException("Patient not found with ID: " + id));
        
        patient.setIsActive(false);
        patientRepository.save(patient);
        
        log.info("Patient soft deleted successfully with ID: {}", id);
    }
    
    @Transactional(readOnly = true)
    public Long getTotalPatientsCount() {
        return patientRepository.countActivePatients();
    }
    
    @Transactional(readOnly = true)
    public Long getTodayRegistrationsCount() {
        return patientRepository.countPatientsRegisteredToday();
    }
    
    private Patient mapToEntity(PatientCreateRequest request) {
        Patient patient = new Patient();
        updatePatientFromRequest(patient, request);
        return patient;
    }
    
    private void updatePatientFromRequest(Patient patient, PatientCreateRequest request) {
        patient.setFirstName(request.getFirstName());
        patient.setLastName(request.getLastName());
        patient.setDateOfBirth(request.getDateOfBirth());
        patient.setGender(request.getGender());
        patient.setPhoneNumber(request.getPhoneNumber());
        patient.setEmail(request.getEmail());
        patient.setAddress(request.getAddress());
        patient.setNationalId(request.getNationalId());
        patient.setEmergencyContactName(request.getEmergencyContactName());
        patient.setEmergencyContactPhone(request.getEmergencyContactPhone());
        patient.setInsuranceProvider(request.getInsuranceProvider());
        patient.setInsuranceNumber(request.getInsuranceNumber());
        patient.setAllergies(request.getAllergies());
        patient.setMedicalHistory(request.getMedicalHistory());
        patient.setNotes(request.getNotes());
    }
    
    private PatientDto mapToDto(Patient patient) {
        PatientDto dto = new PatientDto();
        dto.setId(patient.getId());
        dto.setFirstName(patient.getFirstName());
        dto.setLastName(patient.getLastName());
        dto.setDateOfBirth(patient.getDateOfBirth());
        dto.setGender(patient.getGender());
        dto.setPhoneNumber(patient.getPhoneNumber());
        dto.setEmail(patient.getEmail());
        dto.setAddress(patient.getAddress());
        dto.setNationalId(patient.getNationalId());
        dto.setEmergencyContactName(patient.getEmergencyContactName());
        dto.setEmergencyContactPhone(patient.getEmergencyContactPhone());
        dto.setInsuranceProvider(patient.getInsuranceProvider());
        dto.setInsuranceNumber(patient.getInsuranceNumber());
        dto.setAllergies(patient.getAllergies());
        dto.setMedicalHistory(patient.getMedicalHistory());
        dto.setNotes(patient.getNotes());
        dto.setIsActive(patient.getIsActive());
        dto.setCreatedAt(patient.getCreatedAt());
        dto.setUpdatedAt(patient.getUpdatedAt());
        dto.setFullName(patient.getFullName());
        dto.setAge(patient.getAge());
        return dto;
    }
}
