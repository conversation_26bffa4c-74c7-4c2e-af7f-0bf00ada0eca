# PowerShell script to download portable Java and run the application
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   نظام إدارة عيادة الأسنان" -ForegroundColor Green
Write-Host "   Dental Clinic Management System" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Java is already available
try {
    $javaVersion = java -version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Java متوفر بالفعل" -ForegroundColor Green
        Write-Host $javaVersion[0]
        & .\mvnw.cmd spring-boot:run
        exit
    }
} catch {
    Write-Host "❌ Java غير مثبت" -ForegroundColor Red
}

Write-Host "🔄 جاري تحميل Java المحمول..." -ForegroundColor Yellow

# Create directories
$javaDir = "portable-java"
$tempDir = "temp-download"

if (!(Test-Path $javaDir)) {
    New-Item -ItemType Directory -Path $javaDir -Force | Out-Null
}
if (!(Test-Path $tempDir)) {
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
}

# Download portable Java
$javaUrl = "https://github.com/adoptium/temurin17-binaries/releases/download/jdk-********%2B1/OpenJDK17U-jdk_x64_windows_hotspot_********_1.zip"
$javaZip = "$tempDir\java17.zip"

try {
    Write-Host "📥 تحميل Java 17 المحمول..." -ForegroundColor Yellow
    [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
    Invoke-WebRequest -Uri $javaUrl -OutFile $javaZip -UseBasicParsing
    
    Write-Host "📦 استخراج Java..." -ForegroundColor Yellow
    Expand-Archive -Path $javaZip -DestinationPath $javaDir -Force
    
    # Find the extracted Java directory
    $javaHome = Get-ChildItem -Path $javaDir -Directory | Select-Object -First 1
    $javaExe = Join-Path $javaHome.FullName "bin\java.exe"
    
    if (Test-Path $javaExe) {
        Write-Host "✅ تم تحميل Java بنجاح" -ForegroundColor Green
        
        # Set JAVA_HOME for this session
        $env:JAVA_HOME = $javaHome.FullName
        $env:PATH = "$($javaHome.FullName)\bin;$env:PATH"
        
        Write-Host "🚀 بدء تشغيل التطبيق..." -ForegroundColor Green
        Write-Host ""
        Write-Host "🌐 سيكون التطبيق متاحاً على:" -ForegroundColor Cyan
        Write-Host "   - الصفحة الرئيسية: http://localhost:8080/" -ForegroundColor White
        Write-Host "   - تسجيل الدخول: http://localhost:8080/login" -ForegroundColor White
        Write-Host "   - Swagger API: http://localhost:8080/api/swagger-ui.html" -ForegroundColor White
        Write-Host ""
        Write-Host "🔐 بيانات تسجيل الدخول:" -ForegroundColor Cyan
        Write-Host "   - مدير: admin / admin123" -ForegroundColor White
        Write-Host "   - طبيب: doctor / doctor123" -ForegroundColor White
        Write-Host "   - استقبال: receptionist / receptionist123" -ForegroundColor White
        Write-Host ""
        Write-Host "⏳ جاري تشغيل التطبيق..." -ForegroundColor Yellow
        Write-Host ""
        
        # Run the application
        & .\mvnw.cmd spring-boot:run
        
    } else {
        Write-Host "❌ فشل في العثور على Java المستخرج" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ فشل في تحميل Java: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "📋 يرجى تحميل Java يدوياً من:" -ForegroundColor Yellow
    Write-Host "   https://adoptium.net/" -ForegroundColor White
    Write-Host ""
    Write-Host "أو تشغيل الأمر التالي:" -ForegroundColor Yellow
    Write-Host "   QUICK_START.bat" -ForegroundColor White
}

# Cleanup
if (Test-Path $tempDir) {
    Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host ""
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
