package com.dentalclinic.management.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class InvoiceCreateRequest {
    
    @NotNull(message = "Patient ID is required")
    private Long patientId;
    
    private Long appointmentId;
    
    private LocalDateTime dueDate;
    
    @PositiveOrZero(message = "Tax amount must be positive or zero")
    private BigDecimal taxAmount = BigDecimal.ZERO;
    
    @PositiveOrZero(message = "Discount amount must be positive or zero")
    private BigDecimal discountAmount = BigDecimal.ZERO;
    
    private String notes;
    
    @Valid
    @NotNull(message = "Invoice items are required")
    private List<InvoiceItemCreateRequest> invoiceItems;
}
