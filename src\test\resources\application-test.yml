server:
  port: 8081

spring:
  application:
    name: dental-clinic-management-test
  
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  servlet:
    multipart:
      max-file-size: 1MB
      max-request-size: 1MB

# JWT Configuration for testing
jwt:
  secret: testSecretKey123456789012345678901234567890
  expiration: 3600000 # 1 hour for testing

# File Upload Configuration for testing
file:
  upload-dir: test-uploads/

# Logging Configuration for testing
logging:
  level:
    com.dentalclinic.management: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
